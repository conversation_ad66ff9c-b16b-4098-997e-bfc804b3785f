// To parse this JSON data, do
//
//     final welcome = welcomeFromJson(jsonString);

import 'dart:convert';

TaxesInfo taxesInfoFromJson(String str) => TaxesInfo.fromJson(json.decode(str));

String taxesInfoToJson(TaxesInfo data) => json.encode(data.toJson());

class TaxesInfo {
    String? evaluationResult;
    String? qualification;
    String? qualificationValidFrom;
    String? taxRegister;
    bool? unpaidTaxStatus;
    int? unpaidTaxAmount;
    List<CreditEvaluationList>? creditEvaluationList;
    List<TaxLateFeesList>? taxLateFeesList;
    List<SpiderFinanceInfosYearList>? spiderFinanceInfosYearList;
    List<EnterpriseMonthPaidList>? enterpriseMonthPaidList;
    int? taxDelinquencyRecordCountPast12Months;
    int? last12MaxZeroTaxMonths;
    List<LawRegulationViolationVoList>? lawRegulationViolationVoList;

    TaxesInfo({
        this.evaluationResult,
        this.qualification,
        this.qualificationValidFrom,
        this.taxRegister,
        this.unpaidTaxStatus,
        this.unpaidTaxAmount,
        this.creditEvaluationList,
        this.taxLateFeesList,
        this.spiderFinanceInfosYearList,
        this.enterpriseMonthPaidList,
        this.taxDelinquencyRecordCountPast12Months,
        this.last12MaxZeroTaxMonths,
        this.lawRegulationViolationVoList
    });

    factory TaxesInfo.fromJson(Map<String, dynamic> json) => TaxesInfo(
        evaluationResult: json["evaluationResult"],
        qualification: json["qualification"],
        qualificationValidFrom: json["qualificationValidFrom"],
        taxRegister: json["taxRegister"],
        unpaidTaxStatus: json["unpaidTaxStatus"],
        unpaidTaxAmount: json["unpaidTaxAmount"],
        creditEvaluationList: json["creditEvaluationList"] == null ? [] : List<CreditEvaluationList>.from(json["creditEvaluationList"]!.map((x) => CreditEvaluationList.fromJson(x))),
        taxLateFeesList: json["taxLateFeesList"] == null ? [] : List<TaxLateFeesList>.from(json["taxLateFeesList"]!.map((x) => TaxLateFeesList.fromJson(x))),
        spiderFinanceInfosYearList: json["spiderFinanceInfosYearList"] == null ? [] : List<SpiderFinanceInfosYearList>.from(json["spiderFinanceInfosYearList"]!.map((x) => SpiderFinanceInfosYearList.fromJson(x))),
        enterpriseMonthPaidList: json["enterpriseMonthPaidList"] == null ? [] : List<EnterpriseMonthPaidList>.from(json["enterpriseMonthPaidList"]!.map((x) => EnterpriseMonthPaidList.fromJson(x))),
        taxDelinquencyRecordCountPast12Months: json["taxDelinquencyRecordCountPast12Months"],
        last12MaxZeroTaxMonths: json["last12MaxZeroTaxMonths"],
        lawRegulationViolationVoList: json["lawRegulationViolationVoList"] == null ? [] : List<LawRegulationViolationVoList>.from(json["lawRegulationViolationVoList"]!.map((x) => LawRegulationViolationVoList.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "evaluationResult": evaluationResult,
        "qualification": qualification,
        "qualificationValidFrom": qualificationValidFrom,
        "taxRegister": taxRegister,
        "unpaidTaxStatus": unpaidTaxStatus,
        "unpaidTaxAmount": unpaidTaxAmount,
        "creditEvaluationList": creditEvaluationList == null ? [] : List<dynamic>.from(creditEvaluationList!.map((x) => x.toJson())),
        "taxLateFeesList": taxLateFeesList == null ? [] : List<dynamic>.from(taxLateFeesList!.map((x) => x.toJson())),
        "spiderFinanceInfosYearList": spiderFinanceInfosYearList == null ? [] : List<dynamic>.from(spiderFinanceInfosYearList!.map((x) => x.toJson())),
        "enterpriseMonthPaidList": enterpriseMonthPaidList == null ? [] : List<dynamic>.from(enterpriseMonthPaidList!.map((x) => x.toJson())),
         "taxDelinquencyRecordCountPast12Months": taxDelinquencyRecordCountPast12Months,
         "last12MaxZeroTaxMonths": last12MaxZeroTaxMonths,
        "lawRegulationViolationVoList": lawRegulationViolationVoList,
    };
}

class CreditEvaluationList {
    String? id;
    String? enterpriseId;
    String? evaluationYear;
    int? evaluationScore;
    String? evaluationResult;
    List<String>? indicatorNames;

    CreditEvaluationList({
        this.id,
        this.enterpriseId,
        this.evaluationYear,
        this.evaluationScore,
        this.evaluationResult,
        this.indicatorNames,
    });

    factory CreditEvaluationList.fromJson(Map<String, dynamic> json) => CreditEvaluationList(
        id: json["id"],
        enterpriseId: json["enterpriseId"],
        evaluationYear: json["evaluationYear"],
        evaluationScore: json["evaluationScore"],
        evaluationResult: json["evaluationResult"],
        indicatorNames: json["indicatorNames"] == null ? [] : List<String>.from(json["indicatorNames"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "enterpriseId": enterpriseId,
        "evaluationYear": evaluationYear,
        "evaluationScore": evaluationScore,
        "evaluationResult": evaluationResult,
        "indicatorNames": indicatorNames == null ? [] : List<dynamic>.from(indicatorNames!.map((x) => x)),
    };
}

class EnterpriseMonthPaidList {
  int? month;
   TaxEnterprisePaidDate? taxEnterprisePaidDate;

    EnterpriseMonthPaidList({
        this.month,
        this.taxEnterprisePaidDate,
    });

    factory EnterpriseMonthPaidList.fromJson(Map<String, dynamic> json) => EnterpriseMonthPaidList(
        month: json["month"],
        taxEnterprisePaidDate: json["taxEnterprisePaidDate"] == null ? null : TaxEnterprisePaidDate.fromJson(json["taxEnterprisePaidDate"]),
    );

    Map<String, dynamic> toJson() => {
        "month": month,
         "taxEnterprisePaidDate": taxEnterprisePaidDate?.toJson(),
    };
}

class TaxEnterprisePaidDate {
  String? unpaidTaxAmount;
  String? taxSaleAmount;
  String? taxPaidAmount;
  String? coiled0Months;
  String? debtRatio;
  String? profitRatio;
  String? taxSalesAmount;
  String? vatPayableAmount;
  String? taxIncomeAmount;
  String? taxWaiverAmount;
  String? lateFeesAmount;
  int? lateFeesNum;

  TaxEnterprisePaidDate({
        this.unpaidTaxAmount,
        this.taxSaleAmount,
        this.taxPaidAmount,
        this.coiled0Months,
        this.debtRatio,
        this.profitRatio,
        this.taxSalesAmount,
        this.vatPayableAmount,
        this.taxIncomeAmount,
        this.taxWaiverAmount,
        this.lateFeesAmount,
        this.lateFeesNum,
    });

factory TaxEnterprisePaidDate.fromJson(Map<String, dynamic> json) => TaxEnterprisePaidDate(
        unpaidTaxAmount: json["unpaidTaxAmount"],
        taxSaleAmount: json["taxSaleAmount"],
        taxPaidAmount: json["taxPaidAmount"],
        coiled0Months: json["coiled0Months"],
        debtRatio: json["debtRatio"],
        profitRatio: json["profitRatio"],
        taxSalesAmount: json["taxSalesAmount"],
        vatPayableAmount: json["unpaidTaxAmount"],
        taxIncomeAmount: json["taxIncomeAmount"],
        taxWaiverAmount: json["taxWaiverAmount"],
        lateFeesAmount: json["lateFeesAmount"],
        lateFeesNum: json["lateFeesNum"],
    );

    Map<String, dynamic> toJson() => {
        "unpaidTaxAmount": unpaidTaxAmount,
        "taxSaleAmount": taxSaleAmount,
        "taxPaidAmount": taxPaidAmount,
        "coiled0Months": coiled0Months,
        "debtRatio": debtRatio,
        "profitRatio": profitRatio,
        "taxSalesAmount": taxSalesAmount,
        "vatPayableAmount": vatPayableAmount,
        "taxIncomeAmount": taxIncomeAmount,
        "taxWaiverAmount": taxWaiverAmount,
        "lateFeesAmount": lateFeesAmount,
        "lateFeesNum": lateFeesNum,
    };

}

class SpiderFinanceInfosYearList {
    int? year;
    SpiderFinanceInfoVo? spiderFinanceInfoVo;

    SpiderFinanceInfosYearList({
        this.year,
        this.spiderFinanceInfoVo,
    });

    factory SpiderFinanceInfosYearList.fromJson(Map<String, dynamic> json) => SpiderFinanceInfosYearList(
        year: json["year"],
        spiderFinanceInfoVo: json["spiderFinanceInfoVo"] == null ? null : SpiderFinanceInfoVo.fromJson(json["spiderFinanceInfoVo"]),
    );

    Map<String, dynamic> toJson() => {
        "year": year,
        "spiderFinanceInfoVo": spiderFinanceInfoVo?.toJson(),
    };
}

class SpiderFinanceInfoVo {
    String? totalAssets;
    String? totalLiabilities;
    String? thisYearNetIncrease;
    String? thisYearFinalBalance;
    String? ownerEquity;
    String? liabilitiesOwnerEquity;
    String? businessIncome;
    String? businessProfit;
    String? totalProfit;
    String? netProfits;

    SpiderFinanceInfoVo({
        this.totalAssets,
        this.totalLiabilities,
        this.thisYearNetIncrease,
        this.thisYearFinalBalance,
        this.ownerEquity,
        this.liabilitiesOwnerEquity,
        this.businessIncome,
        this.businessProfit,
        this.totalProfit,
        this.netProfits,
    });

    factory SpiderFinanceInfoVo.fromJson(Map<String, dynamic> json) => SpiderFinanceInfoVo(
        totalAssets: json["totalAssets"],
        totalLiabilities: json["totalLiabilities"],
        thisYearNetIncrease: json["thisYearNetIncrease"],
        thisYearFinalBalance: json["thisYearFinalBalance"],
        ownerEquity: json["ownerEquity"],
        liabilitiesOwnerEquity: json["liabilitiesOwnerEquity"],
        businessIncome: json["businessIncome"],
        businessProfit: json["businessProfit"],
        totalProfit: json["totalProfit"],
        netProfits: json["netProfits"],
    );

    Map<String, dynamic> toJson() => {
        "totalAssets": totalAssets,
        "totalLiabilities": totalLiabilities,
        "thisYearNetIncrease": thisYearNetIncrease,
        "thisYearFinalBalance": thisYearFinalBalance,
        "ownerEquity": ownerEquity,
        "liabilitiesOwnerEquity": liabilitiesOwnerEquity,
        "businessIncome": businessIncome,
        "businessProfit": businessProfit,
        "totalProfit": totalProfit,
        "netProfits": netProfits,
    };
}

class TaxLateFeesList {
    int? id;
    String? taxPeriodStart;
    String? taxPeriodEnd;
    String? paymentDate;
    int? paymentAmount;

    TaxLateFeesList({
        this.id,
        this.taxPeriodStart,
        this.taxPeriodEnd,
        this.paymentDate,
        this.paymentAmount,
    });

    factory TaxLateFeesList.fromJson(Map<String, dynamic> json) => TaxLateFeesList(
        id: json["id"],
        taxPeriodStart: json["taxPeriodStart"],
        taxPeriodEnd: json["taxPeriodEnd"],
        paymentDate: json["paymentDate"],
        paymentAmount: json["paymentAmount"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "taxPeriodStart": taxPeriodStart,
        "taxPeriodEnd": taxPeriodEnd,
        "paymentDate": paymentDate,
        "paymentAmount": paymentAmount,
    };
}


class LawRegulationViolationVoList {
    String? taskId;
    String? actionName;
    String? infractionMeans;
    String? registerDate;
    String? taxPeriodStart;
    String? taxPeriodEnd;
    String? processState;
    String? caseState;
    String? illegalFact;
    String? isSocialSecurityViolation;

    LawRegulationViolationVoList({
        this.taskId,
        this.actionName,
        this.infractionMeans,
        this.registerDate,
        this.taxPeriodStart,
        this.taxPeriodEnd,
        this.processState,
        this.caseState,
        this.illegalFact,
        this.isSocialSecurityViolation
    });

    factory LawRegulationViolationVoList.fromJson(Map<String, dynamic> json) => LawRegulationViolationVoList(
        taskId: json["taskId"],
        actionName: json["actionName"],
        infractionMeans: json["infractionMeans"],
        registerDate: json["registerDate"],
        taxPeriodStart: json["taxPeriodStart"],
        taxPeriodEnd: json["taxPeriodEnd"],
        processState: json["processState"],
        caseState: json["caseState"],
        illegalFact: json["illegalFact"],
        isSocialSecurityViolation: json["isSocialSecurityViolation"],
    );

    Map<String, dynamic> toJson() => {
        "taskId": taskId,
        "actionName": actionName,
        "infractionMeans": infractionMeans,
        "registerDate": registerDate,
        "taxPeriodStart": taxPeriodStart,
        "taxPeriodEnd": taxPeriodEnd,
        "processState": processState,
        "caseState": caseState,
        "illegalFact": illegalFact,
        "isSocialSecurityViolation": isSocialSecurityViolation
    };
}



/// 近三年纳税信息
// To parse this JSON data, do
class Datum {
    String? total;
    int? year;
    List<MonthDatum_Tax>? monthData;

    Datum({
        this.total,
        this.year,
        this.monthData,
    });

    factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        total: json["total"],
        year: json["year"],
        monthData: json["monthData"] == null ? [] : List<MonthDatum_Tax>.from(json["monthData"]!.map((x) => MonthDatum_Tax.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "total": total,
        "year": year,
        "monthData": monthData == null ? [] : List<dynamic>.from(monthData!.map((x) => x.toJson())),
    };
}

class MonthDatum_Tax {
    String? name;
    String? value;

    MonthDatum_Tax({
        this.name,
        this.value,
    });

    factory MonthDatum_Tax.fromJson(Map<String, dynamic> json) => MonthDatum_Tax(
        name: json["name"],
        value: json["value"],
    );

    Map<String, dynamic> toJson() => {
        "name": name,
        "value": value,
    };
}
