import 'package:get/route_manager.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/index.dart';
import 'package:zrreport/pages/location/location_main/location_main_page.dart';
import 'package:zrreport/pages/product/loan_matching/index.dart';

class RoutePages {
  // 路由列表
  static List<GetPage> list = [
    GetPage(
      name: RouteNames.systemMain,
        page: () => MainPage(),
      middlewares: [
      // RouteLoginMiddleware(),
    ]
    ),
    GetPage(
      name: RouteNames.systemLogin,
      page: () => const LoginPage1(),
      transition: Transition.downToUp,
      showCupertinoParallax: false,
    ),
    GetPage(
      name: RouteNames.systemLoginDisableTransition,
      page: () => const LoginPage1(),
      transition: Transition.noTransition,
      showCupertinoParallax: false,
    ),
    GetPage(
      name: RouteNames.systemRegister,
      page: () => const RegisterPage(),
      transition: Transition.noTransition,
      showCupertinoParallax: false,
    ),
    GetPage(
      name: RouteNames.systemWeb,
      page: () => const WebPage(),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: RouteNames.mineEditProfile,
      page: () => const EditProfilePage(),
    ),
    
    GetPage(
      name: RouteNames.queryStep1,
      page: () => QueryStep1Page(
        creditCodeOrTaxPayerId: Get.arguments?['creditName'],
      ),
    ),
    GetPage(
      name: RouteNames.queryStep2,
      page: () => QueryStep2Page(
        info: Get.arguments['company'],
      ),
    ),

    GetPage(
      name: RouteNames.listProvince,
      page: () => ListProvincePage(),
    ),

    GetPage(
      name: RouteNames.usageStatistics,
      page: () => UsageStatisticsPage(),
    ),


    GetPage(
      name: RouteNames.histories,
      page: () => HistoryMain(),
    ),


    GetPage(
      name: RouteNames.reportDetail,
      page: () => ReportDetail(shareCode: Get.arguments['shareCode'], isExample:  Get.arguments['isExample'] ?? false),
    ),


    GetPage(
      name: RouteNames.about,
      page: () => AboutView(),
    ),

    GetPage(
      name: RouteNames.member,
      page: () => MemberPage(),
    ),

    GetPage(
      name: RouteNames.feedback,
      page: () => FeedBackPage(),
    ),


    GetPage(
        name: RouteNames.aricleDetailPage,
      page: () => ArticlePage(articleId: Get.arguments['articleId']),
    ),
    GetPage(
      name: RouteNames.creditServicePoint,
      page: () => const CreditServicePointPage(),
    ),
    GetPage(
      name: RouteNames.locationHistory,
      page: () => LocationMainPage(),
    ),
    GetPage(
      name: RouteNames.loanMatching,
      page: () => const LoanMatchingPage(),
    ),

    GetPage(
      name: RouteNames.matchList,
      page: () => const MatchListPage(),
    ),

  ];
}
