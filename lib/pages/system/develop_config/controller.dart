import 'dart:io';

import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';

class DevelopConfigController extends GetxController {
  // 当前选中的环境
  // ignore: prefer_typing_uninitialized_variables
  final currentEnvironment = AppConfig.to.environment.obs;

  @override
  void onInit() {
    super.onInit();
  }

  // 切换环境
  Future<void> switchEnvironment(Environment environment) async {
    currentEnvironment.value = environment;
  }

  saveEnvironment() {
    if (currentEnvironment.value == AppConfig.to.environment) {
      Loading.toast('与当前配置相同');
      return;
    }

    showCommonDialog(
      Get.context!,
      title: '切换服务器环境',
      content: '确定会关闭app，需要手动启动app生效',
      onConfirm: () async {
        await AppConfig.to.switchEnvironment(currentEnvironment.value);
        Loading.toast('app将会自动关闭');
        await UserService.to.logout();
        await Future.delayed(Duration(seconds: 2));
        exit(0);
      },
    );
  }
}
