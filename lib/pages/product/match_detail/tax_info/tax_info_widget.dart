import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

/// 纳税信息页面
class TaxInfoWidget extends StatelessWidget {
  const TaxInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 40),
          Icon(
            Icons.account_balance,
            size: 64,
            color: AppColors.textColor9,
          ),
          const SizedBox(height: 16),
          Text(
            '纳税信息',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textColor6,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '企业纳税信息内容待开发',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textColor9,
            ),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
