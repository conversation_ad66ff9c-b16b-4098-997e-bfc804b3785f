import 'package:zrreport/common/index.dart';
import '../models/match_detail_models.dart';

/// 匹配详情API服务
class MatchDetailApi {
  /// 获取匹配摘要信息
  static Future<BaseResponse<MatchSummaryData>> getMatchSummary({
    required String id,
    required int type,
  }) async {
    final response = await SXHttpService.to.get(
      '/match/summary/',
      params: {
        'id': id,
        'type': type,
      },
    );
    
    return BaseResponse.fromJson(
      response.data,
      (data) => MatchSummaryData.fromJson(data as Map<String, dynamic>),
    );
  }
}
