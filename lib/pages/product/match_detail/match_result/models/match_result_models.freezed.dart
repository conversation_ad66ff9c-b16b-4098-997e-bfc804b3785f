// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_result_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchResultState {
  int get selectedTabIndex;
  List<BankProduct> get recommendedProducts;
  List<BankProduct> get notMatchedProducts;
  bool get isLoading;
  bool get hasError;
  String? get errorMessage;

  /// Create a copy of MatchResultState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchResultStateCopyWith<MatchResultState> get copyWith =>
      _$MatchResultStateCopyWithImpl<MatchResultState>(
          this as MatchResultState, _$identity);

  /// Serializes this MatchResultState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchResultState &&
            (identical(other.selectedTabIndex, selectedTabIndex) ||
                other.selectedTabIndex == selectedTabIndex) &&
            const DeepCollectionEquality()
                .equals(other.recommendedProducts, recommendedProducts) &&
            const DeepCollectionEquality()
                .equals(other.notMatchedProducts, notMatchedProducts) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedTabIndex,
      const DeepCollectionEquality().hash(recommendedProducts),
      const DeepCollectionEquality().hash(notMatchedProducts),
      isLoading,
      hasError,
      errorMessage);

  @override
  String toString() {
    return 'MatchResultState(selectedTabIndex: $selectedTabIndex, recommendedProducts: $recommendedProducts, notMatchedProducts: $notMatchedProducts, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $MatchResultStateCopyWith<$Res> {
  factory $MatchResultStateCopyWith(
          MatchResultState value, $Res Function(MatchResultState) _then) =
      _$MatchResultStateCopyWithImpl;
  @useResult
  $Res call(
      {int selectedTabIndex,
      List<BankProduct> recommendedProducts,
      List<BankProduct> notMatchedProducts,
      bool isLoading,
      bool hasError,
      String? errorMessage});
}

/// @nodoc
class _$MatchResultStateCopyWithImpl<$Res>
    implements $MatchResultStateCopyWith<$Res> {
  _$MatchResultStateCopyWithImpl(this._self, this._then);

  final MatchResultState _self;
  final $Res Function(MatchResultState) _then;

  /// Create a copy of MatchResultState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTabIndex = null,
    Object? recommendedProducts = null,
    Object? notMatchedProducts = null,
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_self.copyWith(
      selectedTabIndex: null == selectedTabIndex
          ? _self.selectedTabIndex
          : selectedTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      recommendedProducts: null == recommendedProducts
          ? _self.recommendedProducts
          : recommendedProducts // ignore: cast_nullable_to_non_nullable
              as List<BankProduct>,
      notMatchedProducts: null == notMatchedProducts
          ? _self.notMatchedProducts
          : notMatchedProducts // ignore: cast_nullable_to_non_nullable
              as List<BankProduct>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchResultState implements MatchResultState {
  const _MatchResultState(
      {this.selectedTabIndex = 0,
      final List<BankProduct> recommendedProducts = const [],
      final List<BankProduct> notMatchedProducts = const [],
      this.isLoading = false,
      this.hasError = false,
      this.errorMessage})
      : _recommendedProducts = recommendedProducts,
        _notMatchedProducts = notMatchedProducts;
  factory _MatchResultState.fromJson(Map<String, dynamic> json) =>
      _$MatchResultStateFromJson(json);

  @override
  @JsonKey()
  final int selectedTabIndex;
  final List<BankProduct> _recommendedProducts;
  @override
  @JsonKey()
  List<BankProduct> get recommendedProducts {
    if (_recommendedProducts is EqualUnmodifiableListView)
      return _recommendedProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recommendedProducts);
  }

  final List<BankProduct> _notMatchedProducts;
  @override
  @JsonKey()
  List<BankProduct> get notMatchedProducts {
    if (_notMatchedProducts is EqualUnmodifiableListView)
      return _notMatchedProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notMatchedProducts);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? errorMessage;

  /// Create a copy of MatchResultState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchResultStateCopyWith<_MatchResultState> get copyWith =>
      __$MatchResultStateCopyWithImpl<_MatchResultState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchResultStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchResultState &&
            (identical(other.selectedTabIndex, selectedTabIndex) ||
                other.selectedTabIndex == selectedTabIndex) &&
            const DeepCollectionEquality()
                .equals(other._recommendedProducts, _recommendedProducts) &&
            const DeepCollectionEquality()
                .equals(other._notMatchedProducts, _notMatchedProducts) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedTabIndex,
      const DeepCollectionEquality().hash(_recommendedProducts),
      const DeepCollectionEquality().hash(_notMatchedProducts),
      isLoading,
      hasError,
      errorMessage);

  @override
  String toString() {
    return 'MatchResultState(selectedTabIndex: $selectedTabIndex, recommendedProducts: $recommendedProducts, notMatchedProducts: $notMatchedProducts, isLoading: $isLoading, hasError: $hasError, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class _$MatchResultStateCopyWith<$Res>
    implements $MatchResultStateCopyWith<$Res> {
  factory _$MatchResultStateCopyWith(
          _MatchResultState value, $Res Function(_MatchResultState) _then) =
      __$MatchResultStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int selectedTabIndex,
      List<BankProduct> recommendedProducts,
      List<BankProduct> notMatchedProducts,
      bool isLoading,
      bool hasError,
      String? errorMessage});
}

/// @nodoc
class __$MatchResultStateCopyWithImpl<$Res>
    implements _$MatchResultStateCopyWith<$Res> {
  __$MatchResultStateCopyWithImpl(this._self, this._then);

  final _MatchResultState _self;
  final $Res Function(_MatchResultState) _then;

  /// Create a copy of MatchResultState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedTabIndex = null,
    Object? recommendedProducts = null,
    Object? notMatchedProducts = null,
    Object? isLoading = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_MatchResultState(
      selectedTabIndex: null == selectedTabIndex
          ? _self.selectedTabIndex
          : selectedTabIndex // ignore: cast_nullable_to_non_nullable
              as int,
      recommendedProducts: null == recommendedProducts
          ? _self._recommendedProducts
          : recommendedProducts // ignore: cast_nullable_to_non_nullable
              as List<BankProduct>,
      notMatchedProducts: null == notMatchedProducts
          ? _self._notMatchedProducts
          : notMatchedProducts // ignore: cast_nullable_to_non_nullable
              as List<BankProduct>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$BankProduct {
  /// 产品ID
  String get id;

  /// 银行ID
  String get bankId;

  /// 银行名称
  String get bankName;

  /// 银行代码
  String get bankCode;

  /// 产品ID
  String get productId;

  /// 产品图片
  String? get productPic;

  /// 优势数据（JSON字符串）
  String? get advantageData;

  /// 还款方式
  String? get repaymentType;

  /// 贷款期限
  String get loanTerm;

  /// 贷款类型
  String? get loanType;

  /// 产品名称
  String get productName;

  /// 最小额度
  int get productMinQuota;

  /// 最大额度
  int get productMaxQuota;

  /// 最小年利率
  String? get minAnnualRate;

  /// 最大年利率
  String? get maxAnnualRate;

  /// 发布状态
  int get publishStatus;

  /// 匹配ID
  String? get matchId;

  /// 匹配类型
  int get matchType;

  /// 推荐类型 (1=推荐申请, 0=暂不匹配)
  int get recommendType;

  /// 不匹配原因
  String? get reason;

  /// 更新人
  String? get updateBy;

  /// 额度
  String? get quota;

  /// 排序
  int? get sort;

  /// 匹配步骤
  int get matchStep;

  /// 备注
  String? get note;

  /// 创建时间
  String? get createTime;

  /// 更新时间
  String? get updateTime;

  /// 是否收藏
  bool get collect;

  /// 是否置顶
  int get enableTop;

  /// 排序
  int get rankSort;

  /// Create a copy of BankProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BankProductCopyWith<BankProduct> get copyWith =>
      _$BankProductCopyWithImpl<BankProduct>(this as BankProduct, _$identity);

  /// Serializes this BankProduct to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BankProduct &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.bankCode, bankCode) ||
                other.bankCode == bankCode) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productPic, productPic) ||
                other.productPic == productPic) &&
            (identical(other.advantageData, advantageData) ||
                other.advantageData == advantageData) &&
            (identical(other.repaymentType, repaymentType) ||
                other.repaymentType == repaymentType) &&
            (identical(other.loanTerm, loanTerm) ||
                other.loanTerm == loanTerm) &&
            (identical(other.loanType, loanType) ||
                other.loanType == loanType) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productMinQuota, productMinQuota) ||
                other.productMinQuota == productMinQuota) &&
            (identical(other.productMaxQuota, productMaxQuota) ||
                other.productMaxQuota == productMaxQuota) &&
            (identical(other.minAnnualRate, minAnnualRate) ||
                other.minAnnualRate == minAnnualRate) &&
            (identical(other.maxAnnualRate, maxAnnualRate) ||
                other.maxAnnualRate == maxAnnualRate) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            (identical(other.recommendType, recommendType) ||
                other.recommendType == recommendType) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.quota, quota) || other.quota == quota) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.collect, collect) || other.collect == collect) &&
            (identical(other.enableTop, enableTop) ||
                other.enableTop == enableTop) &&
            (identical(other.rankSort, rankSort) ||
                other.rankSort == rankSort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        bankId,
        bankName,
        bankCode,
        productId,
        productPic,
        advantageData,
        repaymentType,
        loanTerm,
        loanType,
        productName,
        productMinQuota,
        productMaxQuota,
        minAnnualRate,
        maxAnnualRate,
        publishStatus,
        matchId,
        matchType,
        recommendType,
        reason,
        updateBy,
        quota,
        sort,
        matchStep,
        note,
        createTime,
        updateTime,
        collect,
        enableTop,
        rankSort
      ]);

  @override
  String toString() {
    return 'BankProduct(id: $id, bankId: $bankId, bankName: $bankName, bankCode: $bankCode, productId: $productId, productPic: $productPic, advantageData: $advantageData, repaymentType: $repaymentType, loanTerm: $loanTerm, loanType: $loanType, productName: $productName, productMinQuota: $productMinQuota, productMaxQuota: $productMaxQuota, minAnnualRate: $minAnnualRate, maxAnnualRate: $maxAnnualRate, publishStatus: $publishStatus, matchId: $matchId, matchType: $matchType, recommendType: $recommendType, reason: $reason, updateBy: $updateBy, quota: $quota, sort: $sort, matchStep: $matchStep, note: $note, createTime: $createTime, updateTime: $updateTime, collect: $collect, enableTop: $enableTop, rankSort: $rankSort)';
  }
}

/// @nodoc
abstract mixin class $BankProductCopyWith<$Res> {
  factory $BankProductCopyWith(
          BankProduct value, $Res Function(BankProduct) _then) =
      _$BankProductCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String bankId,
      String bankName,
      String bankCode,
      String productId,
      String? productPic,
      String? advantageData,
      String? repaymentType,
      String loanTerm,
      String? loanType,
      String productName,
      int productMinQuota,
      int productMaxQuota,
      String? minAnnualRate,
      String? maxAnnualRate,
      int publishStatus,
      String? matchId,
      int matchType,
      int recommendType,
      String? reason,
      String? updateBy,
      String? quota,
      int? sort,
      int matchStep,
      String? note,
      String? createTime,
      String? updateTime,
      bool collect,
      int enableTop,
      int rankSort});
}

/// @nodoc
class _$BankProductCopyWithImpl<$Res> implements $BankProductCopyWith<$Res> {
  _$BankProductCopyWithImpl(this._self, this._then);

  final BankProduct _self;
  final $Res Function(BankProduct) _then;

  /// Create a copy of BankProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? bankId = null,
    Object? bankName = null,
    Object? bankCode = null,
    Object? productId = null,
    Object? productPic = freezed,
    Object? advantageData = freezed,
    Object? repaymentType = freezed,
    Object? loanTerm = null,
    Object? loanType = freezed,
    Object? productName = null,
    Object? productMinQuota = null,
    Object? productMaxQuota = null,
    Object? minAnnualRate = freezed,
    Object? maxAnnualRate = freezed,
    Object? publishStatus = null,
    Object? matchId = freezed,
    Object? matchType = null,
    Object? recommendType = null,
    Object? reason = freezed,
    Object? updateBy = freezed,
    Object? quota = freezed,
    Object? sort = freezed,
    Object? matchStep = null,
    Object? note = freezed,
    Object? createTime = freezed,
    Object? updateTime = freezed,
    Object? collect = null,
    Object? enableTop = null,
    Object? rankSort = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      bankId: null == bankId
          ? _self.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as String,
      bankName: null == bankName
          ? _self.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      bankCode: null == bankCode
          ? _self.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String,
      productId: null == productId
          ? _self.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      productPic: freezed == productPic
          ? _self.productPic
          : productPic // ignore: cast_nullable_to_non_nullable
              as String?,
      advantageData: freezed == advantageData
          ? _self.advantageData
          : advantageData // ignore: cast_nullable_to_non_nullable
              as String?,
      repaymentType: freezed == repaymentType
          ? _self.repaymentType
          : repaymentType // ignore: cast_nullable_to_non_nullable
              as String?,
      loanTerm: null == loanTerm
          ? _self.loanTerm
          : loanTerm // ignore: cast_nullable_to_non_nullable
              as String,
      loanType: freezed == loanType
          ? _self.loanType
          : loanType // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: null == productName
          ? _self.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      productMinQuota: null == productMinQuota
          ? _self.productMinQuota
          : productMinQuota // ignore: cast_nullable_to_non_nullable
              as int,
      productMaxQuota: null == productMaxQuota
          ? _self.productMaxQuota
          : productMaxQuota // ignore: cast_nullable_to_non_nullable
              as int,
      minAnnualRate: freezed == minAnnualRate
          ? _self.minAnnualRate
          : minAnnualRate // ignore: cast_nullable_to_non_nullable
              as String?,
      maxAnnualRate: freezed == maxAnnualRate
          ? _self.maxAnnualRate
          : maxAnnualRate // ignore: cast_nullable_to_non_nullable
              as String?,
      publishStatus: null == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as int,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      matchType: null == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as int,
      recommendType: null == recommendType
          ? _self.recommendType
          : recommendType // ignore: cast_nullable_to_non_nullable
              as int,
      reason: freezed == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _self.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as String?,
      quota: freezed == quota
          ? _self.quota
          : quota // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _self.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateTime: freezed == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      collect: null == collect
          ? _self.collect
          : collect // ignore: cast_nullable_to_non_nullable
              as bool,
      enableTop: null == enableTop
          ? _self.enableTop
          : enableTop // ignore: cast_nullable_to_non_nullable
              as int,
      rankSort: null == rankSort
          ? _self.rankSort
          : rankSort // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _BankProduct implements BankProduct {
  const _BankProduct(
      {required this.id,
      required this.bankId,
      required this.bankName,
      required this.bankCode,
      required this.productId,
      this.productPic,
      this.advantageData,
      this.repaymentType,
      required this.loanTerm,
      this.loanType,
      required this.productName,
      this.productMinQuota = 0,
      this.productMaxQuota = 0,
      this.minAnnualRate,
      this.maxAnnualRate,
      this.publishStatus = 1,
      this.matchId,
      this.matchType = 1,
      this.recommendType = 1,
      this.reason,
      this.updateBy,
      this.quota,
      this.sort,
      this.matchStep = 1,
      this.note,
      this.createTime,
      this.updateTime,
      this.collect = false,
      this.enableTop = 0,
      this.rankSort = 0});
  factory _BankProduct.fromJson(Map<String, dynamic> json) =>
      _$BankProductFromJson(json);

  /// 产品ID
  @override
  final String id;

  /// 银行ID
  @override
  final String bankId;

  /// 银行名称
  @override
  final String bankName;

  /// 银行代码
  @override
  final String bankCode;

  /// 产品ID
  @override
  final String productId;

  /// 产品图片
  @override
  final String? productPic;

  /// 优势数据（JSON字符串）
  @override
  final String? advantageData;

  /// 还款方式
  @override
  final String? repaymentType;

  /// 贷款期限
  @override
  final String loanTerm;

  /// 贷款类型
  @override
  final String? loanType;

  /// 产品名称
  @override
  final String productName;

  /// 最小额度
  @override
  @JsonKey()
  final int productMinQuota;

  /// 最大额度
  @override
  @JsonKey()
  final int productMaxQuota;

  /// 最小年利率
  @override
  final String? minAnnualRate;

  /// 最大年利率
  @override
  final String? maxAnnualRate;

  /// 发布状态
  @override
  @JsonKey()
  final int publishStatus;

  /// 匹配ID
  @override
  final String? matchId;

  /// 匹配类型
  @override
  @JsonKey()
  final int matchType;

  /// 推荐类型 (1=推荐申请, 0=暂不匹配)
  @override
  @JsonKey()
  final int recommendType;

  /// 不匹配原因
  @override
  final String? reason;

  /// 更新人
  @override
  final String? updateBy;

  /// 额度
  @override
  final String? quota;

  /// 排序
  @override
  final int? sort;

  /// 匹配步骤
  @override
  @JsonKey()
  final int matchStep;

  /// 备注
  @override
  final String? note;

  /// 创建时间
  @override
  final String? createTime;

  /// 更新时间
  @override
  final String? updateTime;

  /// 是否收藏
  @override
  @JsonKey()
  final bool collect;

  /// 是否置顶
  @override
  @JsonKey()
  final int enableTop;

  /// 排序
  @override
  @JsonKey()
  final int rankSort;

  /// Create a copy of BankProduct
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BankProductCopyWith<_BankProduct> get copyWith =>
      __$BankProductCopyWithImpl<_BankProduct>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BankProductToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BankProduct &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.bankCode, bankCode) ||
                other.bankCode == bankCode) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productPic, productPic) ||
                other.productPic == productPic) &&
            (identical(other.advantageData, advantageData) ||
                other.advantageData == advantageData) &&
            (identical(other.repaymentType, repaymentType) ||
                other.repaymentType == repaymentType) &&
            (identical(other.loanTerm, loanTerm) ||
                other.loanTerm == loanTerm) &&
            (identical(other.loanType, loanType) ||
                other.loanType == loanType) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.productMinQuota, productMinQuota) ||
                other.productMinQuota == productMinQuota) &&
            (identical(other.productMaxQuota, productMaxQuota) ||
                other.productMaxQuota == productMaxQuota) &&
            (identical(other.minAnnualRate, minAnnualRate) ||
                other.minAnnualRate == minAnnualRate) &&
            (identical(other.maxAnnualRate, maxAnnualRate) ||
                other.maxAnnualRate == maxAnnualRate) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            (identical(other.recommendType, recommendType) ||
                other.recommendType == recommendType) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.quota, quota) || other.quota == quota) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.collect, collect) || other.collect == collect) &&
            (identical(other.enableTop, enableTop) ||
                other.enableTop == enableTop) &&
            (identical(other.rankSort, rankSort) ||
                other.rankSort == rankSort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        bankId,
        bankName,
        bankCode,
        productId,
        productPic,
        advantageData,
        repaymentType,
        loanTerm,
        loanType,
        productName,
        productMinQuota,
        productMaxQuota,
        minAnnualRate,
        maxAnnualRate,
        publishStatus,
        matchId,
        matchType,
        recommendType,
        reason,
        updateBy,
        quota,
        sort,
        matchStep,
        note,
        createTime,
        updateTime,
        collect,
        enableTop,
        rankSort
      ]);

  @override
  String toString() {
    return 'BankProduct(id: $id, bankId: $bankId, bankName: $bankName, bankCode: $bankCode, productId: $productId, productPic: $productPic, advantageData: $advantageData, repaymentType: $repaymentType, loanTerm: $loanTerm, loanType: $loanType, productName: $productName, productMinQuota: $productMinQuota, productMaxQuota: $productMaxQuota, minAnnualRate: $minAnnualRate, maxAnnualRate: $maxAnnualRate, publishStatus: $publishStatus, matchId: $matchId, matchType: $matchType, recommendType: $recommendType, reason: $reason, updateBy: $updateBy, quota: $quota, sort: $sort, matchStep: $matchStep, note: $note, createTime: $createTime, updateTime: $updateTime, collect: $collect, enableTop: $enableTop, rankSort: $rankSort)';
  }
}

/// @nodoc
abstract mixin class _$BankProductCopyWith<$Res>
    implements $BankProductCopyWith<$Res> {
  factory _$BankProductCopyWith(
          _BankProduct value, $Res Function(_BankProduct) _then) =
      __$BankProductCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String bankId,
      String bankName,
      String bankCode,
      String productId,
      String? productPic,
      String? advantageData,
      String? repaymentType,
      String loanTerm,
      String? loanType,
      String productName,
      int productMinQuota,
      int productMaxQuota,
      String? minAnnualRate,
      String? maxAnnualRate,
      int publishStatus,
      String? matchId,
      int matchType,
      int recommendType,
      String? reason,
      String? updateBy,
      String? quota,
      int? sort,
      int matchStep,
      String? note,
      String? createTime,
      String? updateTime,
      bool collect,
      int enableTop,
      int rankSort});
}

/// @nodoc
class __$BankProductCopyWithImpl<$Res> implements _$BankProductCopyWith<$Res> {
  __$BankProductCopyWithImpl(this._self, this._then);

  final _BankProduct _self;
  final $Res Function(_BankProduct) _then;

  /// Create a copy of BankProduct
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? bankId = null,
    Object? bankName = null,
    Object? bankCode = null,
    Object? productId = null,
    Object? productPic = freezed,
    Object? advantageData = freezed,
    Object? repaymentType = freezed,
    Object? loanTerm = null,
    Object? loanType = freezed,
    Object? productName = null,
    Object? productMinQuota = null,
    Object? productMaxQuota = null,
    Object? minAnnualRate = freezed,
    Object? maxAnnualRate = freezed,
    Object? publishStatus = null,
    Object? matchId = freezed,
    Object? matchType = null,
    Object? recommendType = null,
    Object? reason = freezed,
    Object? updateBy = freezed,
    Object? quota = freezed,
    Object? sort = freezed,
    Object? matchStep = null,
    Object? note = freezed,
    Object? createTime = freezed,
    Object? updateTime = freezed,
    Object? collect = null,
    Object? enableTop = null,
    Object? rankSort = null,
  }) {
    return _then(_BankProduct(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      bankId: null == bankId
          ? _self.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as String,
      bankName: null == bankName
          ? _self.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      bankCode: null == bankCode
          ? _self.bankCode
          : bankCode // ignore: cast_nullable_to_non_nullable
              as String,
      productId: null == productId
          ? _self.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      productPic: freezed == productPic
          ? _self.productPic
          : productPic // ignore: cast_nullable_to_non_nullable
              as String?,
      advantageData: freezed == advantageData
          ? _self.advantageData
          : advantageData // ignore: cast_nullable_to_non_nullable
              as String?,
      repaymentType: freezed == repaymentType
          ? _self.repaymentType
          : repaymentType // ignore: cast_nullable_to_non_nullable
              as String?,
      loanTerm: null == loanTerm
          ? _self.loanTerm
          : loanTerm // ignore: cast_nullable_to_non_nullable
              as String,
      loanType: freezed == loanType
          ? _self.loanType
          : loanType // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: null == productName
          ? _self.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      productMinQuota: null == productMinQuota
          ? _self.productMinQuota
          : productMinQuota // ignore: cast_nullable_to_non_nullable
              as int,
      productMaxQuota: null == productMaxQuota
          ? _self.productMaxQuota
          : productMaxQuota // ignore: cast_nullable_to_non_nullable
              as int,
      minAnnualRate: freezed == minAnnualRate
          ? _self.minAnnualRate
          : minAnnualRate // ignore: cast_nullable_to_non_nullable
              as String?,
      maxAnnualRate: freezed == maxAnnualRate
          ? _self.maxAnnualRate
          : maxAnnualRate // ignore: cast_nullable_to_non_nullable
              as String?,
      publishStatus: null == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as int,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      matchType: null == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as int,
      recommendType: null == recommendType
          ? _self.recommendType
          : recommendType // ignore: cast_nullable_to_non_nullable
              as int,
      reason: freezed == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _self.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as String?,
      quota: freezed == quota
          ? _self.quota
          : quota // ignore: cast_nullable_to_non_nullable
              as String?,
      sort: freezed == sort
          ? _self.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as int?,
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateTime: freezed == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      collect: null == collect
          ? _self.collect
          : collect // ignore: cast_nullable_to_non_nullable
              as bool,
      enableTop: null == enableTop
          ? _self.enableTop
          : enableTop // ignore: cast_nullable_to_non_nullable
              as int,
      rankSort: null == rankSort
          ? _self.rankSort
          : rankSort // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$MatchResultResponse {
  List<BankProduct> get products;

  /// Create a copy of MatchResultResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchResultResponseCopyWith<MatchResultResponse> get copyWith =>
      _$MatchResultResponseCopyWithImpl<MatchResultResponse>(
          this as MatchResultResponse, _$identity);

  /// Serializes this MatchResultResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchResultResponse &&
            const DeepCollectionEquality().equals(other.products, products));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(products));

  @override
  String toString() {
    return 'MatchResultResponse(products: $products)';
  }
}

/// @nodoc
abstract mixin class $MatchResultResponseCopyWith<$Res> {
  factory $MatchResultResponseCopyWith(
          MatchResultResponse value, $Res Function(MatchResultResponse) _then) =
      _$MatchResultResponseCopyWithImpl;
  @useResult
  $Res call({List<BankProduct> products});
}

/// @nodoc
class _$MatchResultResponseCopyWithImpl<$Res>
    implements $MatchResultResponseCopyWith<$Res> {
  _$MatchResultResponseCopyWithImpl(this._self, this._then);

  final MatchResultResponse _self;
  final $Res Function(MatchResultResponse) _then;

  /// Create a copy of MatchResultResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? products = null,
  }) {
    return _then(_self.copyWith(
      products: null == products
          ? _self.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<BankProduct>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchResultResponse implements MatchResultResponse {
  const _MatchResultResponse({required final List<BankProduct> products})
      : _products = products;
  factory _MatchResultResponse.fromJson(Map<String, dynamic> json) =>
      _$MatchResultResponseFromJson(json);

  final List<BankProduct> _products;
  @override
  List<BankProduct> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  /// Create a copy of MatchResultResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchResultResponseCopyWith<_MatchResultResponse> get copyWith =>
      __$MatchResultResponseCopyWithImpl<_MatchResultResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchResultResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchResultResponse &&
            const DeepCollectionEquality().equals(other._products, _products));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_products));

  @override
  String toString() {
    return 'MatchResultResponse(products: $products)';
  }
}

/// @nodoc
abstract mixin class _$MatchResultResponseCopyWith<$Res>
    implements $MatchResultResponseCopyWith<$Res> {
  factory _$MatchResultResponseCopyWith(_MatchResultResponse value,
          $Res Function(_MatchResultResponse) _then) =
      __$MatchResultResponseCopyWithImpl;
  @override
  @useResult
  $Res call({List<BankProduct> products});
}

/// @nodoc
class __$MatchResultResponseCopyWithImpl<$Res>
    implements _$MatchResultResponseCopyWith<$Res> {
  __$MatchResultResponseCopyWithImpl(this._self, this._then);

  final _MatchResultResponse _self;
  final $Res Function(_MatchResultResponse) _then;

  /// Create a copy of MatchResultResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? products = null,
  }) {
    return _then(_MatchResultResponse(
      products: null == products
          ? _self._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<BankProduct>,
    ));
  }
}

// dart format on
