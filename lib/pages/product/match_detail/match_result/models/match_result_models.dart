import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_result_models.freezed.dart';
part 'match_result_models.g.dart';

/// 匹配结果页面状态
@freezed
abstract class MatchResultState with _$MatchResultState {
  const factory MatchResultState({
    @Default(0) int selectedTabIndex,
    @Default([]) List<BankProduct> recommendedProducts,
    @Default([]) List<BankProduct> notMatchedProducts,
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
  }) = _MatchResultState;

  factory MatchResultState.fromJson(Map<String, dynamic> json) =>
      _$MatchResultStateFromJson(json);
}

/// 银行产品模型
@freezed
abstract class BankProduct with _$BankProduct {
  const factory BankProduct({
    /// 产品ID
    required String id,
    /// 银行ID
    required String bankId,
    /// 银行名称
    required String bankName,
    /// 银行代码
    required String bankCode,
    /// 产品ID
    required String productId,
    /// 产品图片
    String? productPic,
    /// 优势数据（JSON字符串）
    String? advantageData,
    /// 还款方式
    String? repaymentType,
    /// 贷款期限
    required String loanTerm,
    /// 贷款类型
    String? loanType,
    /// 产品名称
    required String productName,
    /// 最小额度
    @Default(0) int productMinQuota,
    /// 最大额度
    @Default(0) int productMaxQuota,
    /// 最小年利率
    String? minAnnualRate,
    /// 最大年利率
    String? maxAnnualRate,
    /// 发布状态
    @Default(1) int publishStatus,
    /// 匹配ID
    String? matchId,
    /// 匹配类型
    @Default(1) int matchType,
    /// 推荐类型 (1=推荐申请, 0=暂不匹配)
    @Default(1) int recommendType,
    /// 不匹配原因
    String? reason,
    /// 更新人
    String? updateBy,
    /// 额度
    String? quota,
    /// 排序
    int? sort,
    /// 匹配步骤
    @Default(1) int matchStep,
    /// 备注
    String? note,
    /// 创建时间
    String? createTime,
    /// 更新时间
    String? updateTime,
    /// 是否收藏
    @Default(false) bool collect,
    /// 是否置顶
    @Default(0) int enableTop,
    /// 排序
    @Default(0) int rankSort,
  }) = _BankProduct;

  factory BankProduct.fromJson(Map<String, dynamic> json) =>
      _$BankProductFromJson(json);
}

/// BankProduct扩展方法
extension BankProductExtension on BankProduct {
  /// 获取格式化的最高额度
  String get formattedMaxAmount {
    return productMaxQuota > 0 ? '$productMaxQuota' : '面议';
  }

  /// 获取格式化的年利率
  String get formattedInterestRate {
    if (minAnnualRate != null && maxAnnualRate != null) {
      return '$minAnnualRate%～$maxAnnualRate%';
    } else if (minAnnualRate != null) {
      return '$minAnnualRate%起';
    } else if (maxAnnualRate != null) {
      return '最高$maxAnnualRate%';
    }
    return '面议';
  }

  /// 是否为推荐产品
  bool get isRecommended => recommendType == 1;

  /// 是否为税贷产品（根据产品名称判断）
  bool get isTaxProduct => true;

  /// 是否为热门产品（根据置顶状态判断）
  bool get isHotProduct => enableTop == 1;

  /// 获取不匹配原因或特殊说明
  String get specialNote => reason ?? note ?? '';

  /// 获取优势列表
  List<String> get advantages {
    if (advantageData == null || advantageData!.isEmpty) return [];
    try {
      // 解析JSON数组字符串
      final decoded = json.decode(advantageData!) as List;
      return decoded.map((e) => e.toString()).toList();
    } catch (e) {
      return [];
    }
  }
}

/// 匹配结果API响应
@freezed
abstract class MatchResultResponse with _$MatchResultResponse {
  const factory MatchResultResponse({
    required List<BankProduct> products,
  }) = _MatchResultResponse;

  factory MatchResultResponse.fromJson(Map<String, dynamic> json) =>
      _$MatchResultResponseFromJson(json);
}
