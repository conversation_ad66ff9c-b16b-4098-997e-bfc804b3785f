import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/match_result_models.dart';
import '../api/match_result_api.dart';

part 'match_result_provider.g.dart';

/// 匹配结果页面状态管理
@riverpod
class MatchResultNotifier extends _$MatchResultNotifier {
  @override
  MatchResultState build() {
    return const MatchResultState();
  }

  /// 初始化页面数据
  Future<void> initialize({
    required String matchId,
  }) async {
    state = state.copyWith(isLoading: true, hasError: false, errorMessage: null);

    try {
      // 并行调用两个API获取推荐产品和暂不匹配产品
      final results = await Future.wait([
        MatchResultApi.getMatchProducts(
            id: matchId, recommendType: 1), // 推荐申请产品
        MatchResultApi.getMatchProducts(
            id: matchId, recommendType: 2), // 暂不匹配产品
      ]);

      final recommendedResponse = results[0];
      final notMatchedResponse = results[1];

      state = state.copyWith(
        recommendedProducts: recommendedResponse.data ?? [],
        notMatchedProducts: notMatchedResponse.data ?? [],
        isLoading: false,
      );

    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        hasError: true,
        errorMessage: e.toString(),
      );
    }
  }

  /// 切换标签页
  void selectTab(int index) {
    state = state.copyWith(selectedTabIndex: index);
  }

  /// 重新加载数据
  Future<void> reload({
    required String matchId,
  }) async {
    await initialize(matchId: matchId);
  }
}
