// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_result_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$matchResultNotifierHash() =>
    r'6d408ad75526a14c818844672752db55ada1622a';

/// 匹配结果页面状态管理
///
/// Copied from [MatchResultNotifier].
@ProviderFor(MatchResultNotifier)
final matchResultNotifierProvider =
    AutoDisposeNotifierProvider<MatchResultNotifier, MatchResultState>.internal(
  MatchResultNotifier.new,
  name: r'matchResultNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$matchResultNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MatchResultNotifier = AutoDisposeNotifier<MatchResultState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
