import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/index.dart';
import 'models/match_result_models.dart';
import 'provider/match_result_provider.dart';

/// 标签页信息数据类
/// 匹配结果组件 - 负责展示推荐申请产品和暂不匹配产品的标签页和列表
class MatchResultWidget extends ConsumerStatefulWidget {
  /// 匹配ID
  final String matchId;

  /// 匹配类型
  final int matchType;

  /// 创建时间
  final String createTime;

  const MatchResultWidget({
    super.key,
    required this.matchId,
    required this.matchType,
    required this.createTime,
  });

  @override
  ConsumerState<MatchResultWidget> createState() => _MatchResultWidgetState();
}

class _MatchResultWidgetState extends ConsumerState<MatchResultWidget> {
  final tipTextStyle = TextStyle(
      color: AppColors.textColor9, fontWeight: FontWeight.bold, fontSize: 13);

  @override
  void initState() {
    super.initState();

    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(matchResultNotifierProvider.notifier).initialize(
            matchId: widget.matchId,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(matchResultNotifierProvider);
    final notifier = ref.read(matchResultNotifierProvider.notifier);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标签页
        _buildTabBar(state),
        const SizedBox(height: 16),

        // 标签页内容
        _buildTabContent(state, notifier),
      ],
    );
  }

  /// 构建标签页
  Widget _buildTabBar(MatchResultState state) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Row(children: [
          _buildTabItem(
                 '推荐申请产品', state.recommendedProducts.length, 0,
              state.selectedTabIndex),
          _buildTabItem(
                 '暂不匹配产品', state.notMatchedProducts.length, 1,
              state.selectedTabIndex),
          ],
            ),
      ),
    );
  }

  /// 构建单个标签项
  Widget _buildTabItem(String title, int count, int index, int selectedIndex) {
    final isSelected = selectedIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () =>
            ref.read(matchResultNotifierProvider.notifier).selectTab(index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : AppColors.grey,
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Center(
            child: Text(
              '$title($count)',
              style: TextStyle(
                fontSize: 16,
                color: isSelected ? Colors.white : AppColors.textColor6,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建标签页内容
  Widget _buildTabContent(
      MatchResultState state, MatchResultNotifier notifier) {
    if (state.isLoading) {
      return Container(
        constraints: const BoxConstraints(minHeight: 150),
        padding: const EdgeInsets.all(16),
        child: const Center(child: LoadingWidget()),
      );
    }

    if (state.hasError) {
      return Container(
        constraints: const BoxConstraints(minHeight: 200),
        padding: const EdgeInsets.all(16),
        child: Center(
          child: ErrorStatusWidget(
            text: state.errorMessage ?? "加载失败，点击重试",
            onAttempt: () => notifier.reload(
              matchId: widget.matchId,
            ),
          ),
        ),
      );
    }

    // 根据选中的标签页显示对应的产品列表
    final products = state.selectedTabIndex == 0
        ? state.recommendedProducts // 推荐申请产品 (recommendType=1)
        : state.notMatchedProducts; // 暂不匹配产品 (recommendType=2)

    return _buildProductList(products);
  }

  /// 构建产品列表
  Widget _buildProductList(List<BankProduct> products) {
    if (products.isEmpty) {
      return Container(
        constraints: const BoxConstraints(minHeight: 200),
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: EmptyWidget(text: "暂无产品数据"),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            products
            .map((product) => GestureDetector(
                onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoanDetailPage(
                          productId: product.productId, // 商品ID
                        ),
                      ),
                    ),
                child: _buildProductCard(product)))
            .toList(),
      ),
    );
  }

  /// 构建产品卡片
  Widget _buildProductCard(BankProduct product) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 银行logo和产品名称
          Row(
            children: [
              // 银行logo

              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                    imageUrl: product.productPic ?? '', width: 60, height: 60),
              ),

              const SizedBox(width: 12),

              // 产品名称和标签
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.productName,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.textColor1,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // 标签
                    Row(
                      children: [
                        if (product.isTaxProduct) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.orange,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              '税贷',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 6),
                        ],
                        if (product.isHotProduct) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.local_fire_department,
                                  size: 10,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 2),
                                const Text(
                                  '热门',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 产品信息
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('最高额度', '${product.formattedMaxAmount}万'),
              ),
              Expanded(
                child: _buildInfoItem('贷款期限', '${product.loanTerm}期'),
              ),
            ],
          ),
          const SizedBox(height: 8),

          _buildInfoItem('年利率', product.formattedInterestRate),

          // 特殊说明
          if (product.specialNote.isNotEmpty && product.recommendType == 2) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                product.specialNote,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textColor6,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Row(
      children: [
        Text(
          '$label：',
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textColor9,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textColor9,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }


}
