import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/match_detail_models.dart';
import '../api/match_detail_api.dart';

part 'match_detail_provider.g.dart';

/// 匹配详情页面状态管理
@riverpod
class MatchDetailNotifier extends _$MatchDetailNotifier {
  @override
  MatchDetailState build() {
    return const MatchDetailState();
  }

  /// 初始化页面数据
  Future<void> initialize(MatchDetailParams params) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    
    try {
      final response = await MatchDetailApi.getMatchSummary(
        id: params.id,
        type: params.type,
      );
      
      state = state.copyWith(
        summaryData: response.data,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 切换标签页
  void selectTab(int index) {
    state = state.copyWith(selectedTabIndex: index);
  }

  /// 重新加载数据
  Future<void> reload(MatchDetailParams params) async {
    await initialize(params);
  }
}
