import 'package:zrreport/common/index.dart';
import '../model/enterprise_info.dart';

/// 企业信息API服务
class EnterpriseInfoApi {
  /// 获取企业信息
  /// [id] 企业ID
  static Future<BaseResponse<EnterpriseInfo>> getEnterpriseInfo(String id) async {
    final response = await SXHttpService.to.get(
      '/cmsMatchEnterprise/getEnterpriseReport/$id',
    );
    
    return BaseResponse.fromJson(
      response.data,
      (data) => EnterpriseInfo.fromJson(data),
    );
  }
}
