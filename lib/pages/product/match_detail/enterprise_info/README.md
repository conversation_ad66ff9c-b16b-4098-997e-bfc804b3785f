# 企业信息页面

## 功能描述

企业信息页面用于显示匹配产品的企业详细信息，包括企业基本信息、法人信息、注册信息等。

## 文件结构

```
enterprise_info/
├── README.md                    # 说明文档
├── index.dart                   # 导出文件
├── enterprise_info_widget.dart  # 主页面组件
├── api/
│   └── enterprise_info_api.dart # API服务
├── model/
│   ├── enterprise_info.dart     # 数据模型
│   ├── enterprise_info.freezed.dart  # 生成的freezed文件
│   └── enterprise_info.g.dart   # 生成的json序列化文件
└── provider/
    ├── enterprise_info_provider.dart # Riverpod状态管理
    ├── enterprise_info_provider.freezed.dart # 生成的freezed文件
    └── enterprise_info_provider.g.dart # 生成的riverpod文件
```

## 主要功能

### 1. 数据模型 (model/enterprise_info.dart)

- **EnterpriseInfo**: 企业信息主模型
- **QccEnterprisePartner**: 企业合作伙伴信息
- **EnterpriseEmployee**: 企业员工信息

### 2. API服务 (api/enterprise_info_api.dart)

- `getEnterpriseInfo(String id)`: 获取企业信息

### 3. 状态管理 (provider/enterprise_info_provider.dart)

- **EnterpriseInfoState**: 页面状态
- **EnterpriseInfoNotifier**: 状态管理器
- `fetchEnterpriseInfo(String id)`: 获取企业信息
- `reload(String id)`: 重新加载数据

### 4. UI组件 (enterprise_info_widget.dart)

- 蓝色卡片样式的企业信息展示
- 使用 `AdaptiveTextRow` 组件显示各项数据
- 支持加载状态、错误状态和空数据状态
- 统一信用代码支持复制功能

## 使用方法

```dart
// 在页面中使用企业信息组件
EnterpriseInfoWidget(
  enterpriseId: 'your_enterprise_id',
)
```

## API接口

### 获取企业信息

**接口地址**: `/cmsMatchEnterprise/getEnterpriseReport/{id}`

**请求方法**: GET

**参数说明**:
- `id`: 企业ID

**返回数据示例**:
```json
{
  "traceId": "0682397a498c42f6ae044af6b58b8221",
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "850",
    "enterpriseId": "1920",
    "creditCode": "91140624MA0KKBM98J",
    "legalPerson": "辛雅楠",
    "industry": "服装零售",
    "enterpriseName": "怀仁市怀通商贸有限公司",
    "loginRegisterType": "其他有限责任公司",
    "phone": "***********",
    "age": 34,
    "registerDate": "2019-06-13",
    "registerMonth": 72,
    "registeredCapital": "200000.00",
    "stockStatus": false,
    "openAccountBank": "",
    "registerAddress": "怀仁市怀贤西街汇通源底商8-10号",
    "qccEnterprisePartnersList": [...],
    "enterpriseEmployeesList": [...]
  }
}
```

## 显示字段

页面按照UI设计显示以下字段：

1. **企业名称** - 作为卡片标题显示
2. **法人年龄(岁)** - 显示法人年龄，无数据时显示"-"
3. **法人股份占比(%)** - 显示法人持股比例，无数据时显示"-"
4. **统一信用代码** - 支持复制功能
5. **当前法人姓名** - 显示法人姓名
6. **基本户** - 显示开户银行，无数据时显示"-"
7. **注册类型** - 显示登记注册类型
8. **注册区域** - 显示注册地址，支持多行显示
9. **成立日期** - 显示注册日期
10. **成立时长** - 显示注册月数
11. **行业分类** - 显示所属行业
12. **法人是否占股** - 根据stockStatus显示"是"或"否"

## 错误处理

- 网络请求失败时显示 `ErrorStatusWidget`
- 数据为空时显示 `EmptyWidget`
- 加载过程中显示 `LoadingWidget`

## 注意事项

1. 企业ID通过页面参数传入
2. 使用了freezed进行数据模型的不可变性管理
3. 使用了riverpod进行状态管理
4. 遵循了项目的统一代码规范和样式指南
