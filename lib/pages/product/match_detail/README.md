# 匹配详情页面实现

## 功能概述

匹配详情页面用于展示企业匹配产品的详细信息，包括匹配摘要、企业分析详情等内容。

## 目录结构

```
lib/pages/product/match_detail/
├── api/
│   └── match_detail_api.dart          # API服务
├── models/
│   ├── match_detail_models.dart       # 数据模型
│   ├── match_detail_models.freezed.dart
│   └── match_detail_models.g.dart
├── provider/
│   ├── match_detail_provider.dart     # 状态管理
│   └── match_detail_provider.g.dart
├── enterprise_info/
│   └── enterprise_info_widget.dart    # 企业信息页面
├── tax_info/
│   └── tax_info_widget.dart           # 纳税信息页面
├── invoice_info/
│   └── invoice_info_widget.dart       # 开票信息页面
├── match_result/
│   └── match_result_widget.dart       # 匹配结果页面
├── widgets/                           # 通用组件目录
├── match_detail_page.dart             # 主页面
├── index.dart                         # 导出文件
├── example_usage.dart                 # 使用示例
└── README.md                          # 说明文档
```

## 主要功能

### 1. 匹配摘要卡片
- 显示企业名称
- 显示报告生成时间
- 显示准入产品数量和最高可贷金额
- 蓝色渐变背景设计

### 2. 标签页切换
- 企业信息
- 纳税信息
- 开票信息
- 匹配结果（默认选中）

### 3. 匹配结果页面
- 推荐申请产品和暂不匹配产品切换
- 产品卡片展示（银行logo、产品名称、额度、期限、利率等）

### 4. 分享功能
- 分享方案按钮固定在页面底部（非滚动区域）
- 带有阴影效果的白色背景
- 只在有数据时显示

## 数据模型

### MatchDetailParams
页面参数模型，包含：
- `id`: 匹配ID
- `type`: 匹配类型

### MatchSummaryData
匹配摘要数据模型，包含：
- `enterpriseName`: 企业名称
- `matchStatus`: 匹配状态
- `matchStep`: 匹配步骤
- `reportCreateTime`: 报告生成时间
- `productNum`: 产品数量
- `totalQuota`: 总额度
- `enterpriseId`: 企业ID
- `creditCode`: 信用代码
- `question`: 是否有问题
- `memberId`: 会员ID

### MatchDetailState
页面状态模型，包含：
- `selectedTabIndex`: 当前选中的标签页索引
- `summaryData`: 摘要数据
- `isLoading`: 加载状态
- `errorMessage`: 错误信息

## API接口

### 获取匹配摘要
- **接口**: `/portal/match/summary/`
- **方法**: GET
- **参数**: 
  - `id`: 匹配ID
  - `type`: 匹配类型
- **返回**: MatchSummaryData

## 使用方法

```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'lib/pages/product/match_detail/index.dart';

// 创建参数
final params = MatchDetailParams(
  id: 'your_match_id',
  type: 1,
);

// 导航到页面
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ProviderScope(
      child: MatchDetailPage(params: params),
    ),
  ),
);
```

## 状态管理

使用Riverpod进行状态管理：

```dart
// 获取状态
final state = ref.watch(matchDetailNotifierProvider);

// 获取notifier
final notifier = ref.read(matchDetailNotifierProvider.notifier);

// 初始化数据
notifier.initialize(params);

// 切换标签页
notifier.selectTab(index);

// 重新加载
notifier.reload(params);
```

## 错误处理

- 网络请求失败时显示ErrorStatusWidget
- 数据为空时显示EmptyWidget
- 加载中显示LoadingWidget

## 注意事项

1. 需要运行 `dart run build_runner build` 生成freezed和riverpod相关文件
2. 确保项目中已正确配置网络请求服务
3. 页面使用了项目的通用颜色系统AppColors
4. 子页面（企业信息、纳税信息、开票信息）目前为占位实现，需要根据具体需求开发
5. 匹配结果页面的产品数据目前为示例数据，需要接入真实API

## 待完善功能

1. 企业信息页面的具体实现
2. 纳税信息页面的具体实现
3. 开票信息页面的具体实现
4. 匹配结果页面的真实数据接入
5. 分享功能的实现
6. 更多操作功能的实现
