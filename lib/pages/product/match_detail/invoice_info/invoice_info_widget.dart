import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

/// 开票信息页面
class InvoiceInfoWidget extends StatelessWidget {
  const InvoiceInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 40),
          Icon(
            Icons.receipt_long,
            size: 64,
            color: AppColors.textColor9,
          ),
          const SizedBox(height: 16),
          Text(
            '开票信息',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textColor6,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '企业开票信息内容待开发',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textColor9,
            ),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
