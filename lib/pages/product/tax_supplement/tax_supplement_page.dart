import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart';
import 'package:zrreport/common/index.dart';
import 'provider/tax_supplement_provider.dart';
import 'models/tax_supplement_models.dart';

/// 税务匹配补充材料页面
class TaxSupplementPage extends ConsumerStatefulWidget {
  /// 纳税人识别号
  final String creditCode;

  /// 公司名称
  final String enterpriseName;

  /// 省份ID
  final String provinceId;

  /// 申请人身份
  final String personStand;

  const TaxSupplementPage({
    super.key,
    required this.creditCode,
    required this.enterpriseName,
    required this.provinceId,
    required this.personStand,
  });

  @override
  ConsumerState<TaxSupplementPage> createState() => _TaxSupplementPageState();
}

class _TaxSupplementPageState extends ConsumerState<TaxSupplementPage> {
  // final _taxAccountController = TextEditingController(text: "***********");
  // final _taxPasswordController = TextEditingController(text: "HNtax168168");

  final _taxAccountController = TextEditingController(text: "***********");
  final _taxPasswordController = TextEditingController(text: "Aa12345678.");
  final _captchaController = TextEditingController();
  bool _obscurePassword = true;

  final contentStyle = TextStyle(color: AppColors.textColor3, fontSize: 14);

  @override
  void initState() {
    super.initState();
    // 初始化页面数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(taxSupplementNotifierProvider.notifier).initializeData(
            creditCode: widget.creditCode,
            enterpriseName: widget.enterpriseName,
            provinceId: widget.provinceId,
            personStand: widget.personStand,
          );

      ref
          .read(taxSupplementNotifierProvider.notifier)
          .updateTaxAccount(_taxAccountController.text);

      ref
          .read(taxSupplementNotifierProvider.notifier)
          .updateTaxPassword(_taxPasswordController.text);
    });
  }

  @override
  void dispose() {
    _taxAccountController.dispose();
    _taxPasswordController.dispose();
    _captchaController.dispose();
    // AutoDispose provider会自动清理资源，无需手动调用cleanup
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(taxSupplementNotifierProvider);
    final notifier = ref.read(taxSupplementNotifierProvider.notifier);

    // 如果显示成功页面
    if (state.showSuccessPage) {
      return _buildSuccessPage(context, notifier);
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('补充资料'),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // _buildCompanyInfoSection(state),

              _buildInfoRow('公司名称', state.enterpriseName),
              const SizedBox(height: 16),
              _buildInfoRow('纳税人识别号', state.creditCode),
              const SizedBox(height: 16),
              _buildProvinceRow(state),

              const SizedBox(height: 16),

              _buildTaxAccountSection(state, notifier),
              const SizedBox(height: 16),
              _buildPasswordSection(state, notifier),
              if (state.needsCaptcha) ...[
                const SizedBox(height: 16),
                _buildCaptchaSection(state, notifier),
              ],
              const SizedBox(height: 32),
              _buildNextButton(state, notifier),
              const SizedBox(height: 24),
              _buildAgreementSection(state, notifier),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRowHeader(String label, {bool showPrefixIcon = false}) {
    return Row(
      children: [
        if (showPrefixIcon)
          Icon(
            Icons.error_outline,
            size: 20,
            color: AppColors.textColor8,
          ),
        if (showPrefixIcon) SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 15,
            color: Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        const Text(
          ' *',
          style: TextStyle(color: Colors.red, fontSize: 14),
        ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRowHeader(label),
        const SizedBox(height: 8),
        Text(
          value,
          style: contentStyle,
        ),
        Divider(
          color: AppColors.dividerColor,
          thickness: 1,
          height: 16,
        ),
      ],
    );
  }

  /// 构建省份行
  Widget _buildProvinceRow(TaxSupplementState state) {
    return Consumer(
      builder: (context, ref, child) {
        final provinceState = ref.watch(provinceListNotifierProvider);
        return provinceState.when(
          data: (provinces) {
            final province = provinces.firstWhere(
              (p) => p.id == state.provinceId,
              orElse: () => Province(
                id: state.provinceId,
                name: '未知省份',
                pinyin: '',
                areaId: '',
                status: 1,
                deleteStatus: 0,
                createBy: '',
                createTime: '',
                updateBy: '',
                updateTime: '',
                invoiceStatus: '',
                taxStatus: '',
                taxLoginUrl: '',
              ),
            );
            return _buildInfoRow(
              '所属省份',
              province.name ?? '未知省份',
            );
          },
          loading: () => _buildInfoRow(
            '所属省份',
            '加载中...',
          ),
          error: (error, stack) => _buildInfoRow(
            '所属省份',
            '加载失败',
          ),
        );
      },
    );
  }

  /// 构建税务账号输入部分
  Widget _buildTaxAccountSection(
      TaxSupplementState state, TaxSupplementNotifier notifier) {
    return Column(
      children: [
        _buildRowHeader('电子税务局账号', showPrefixIcon: true),
        TextField(
          controller: _taxAccountController,
          keyboardType: TextInputType.phone,
          style: contentStyle,
          decoration: const InputDecoration(
            hintText: '请输入税务账号',
            border: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
          ),
          onChanged: notifier.updateTaxAccount,
        ),
      ],
    );
  }

  /// 构建密码输入部分
  Widget _buildPasswordSection(
      TaxSupplementState state, TaxSupplementNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRowHeader('税务密码', showPrefixIcon: true),
        TextField(
          controller: _taxPasswordController,
          obscureText: _obscurePassword,
          style: contentStyle,
          decoration: InputDecoration(
            hintText: '请输入税务密码',
            border: const UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off : Icons.visibility,
                color: const Color(0xFF999999),
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
          ),
          onChanged: notifier.updateTaxPassword,
        ),
      ],
    );
  }

  /// 构建验证码输入部分
  Widget _buildCaptchaSection(
      TaxSupplementState state, TaxSupplementNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildRowHeader('验证码', showPrefixIcon: true),
        Row(
          children: [
            Expanded(
              child: TextField(
                style: contentStyle,
                controller: _captchaController,
                decoration: const InputDecoration(
                  hintText: '请输入验证码',
                  border: InputBorder.none,
                  // border: UnderlineInputBorder(
                  //   borderSide: BorderSide(color: AppColors.dividerColor),
                  // ),
                  // enabledBorder: UnderlineInputBorder(
                  //   borderSide: BorderSide(color: AppColors.dividerColor),
                  // ),
                  // focusedBorder: UnderlineInputBorder(
                  //   borderSide: BorderSide(color: AppColors.dividerColor),
                  // ),
                ),
                onChanged: notifier.updateCaptchaCode,
              ),
            ),
            const SizedBox(width: 12),
            buildFilledButton(
              state.captchaCountdown > 0
                  ? '${state.captchaCountdown}s'
                  : '获取验证码',
              width: 100,
              backgroundColor: state.captchaCountdown > 0
                  ? AppColors.disableBackground
                  : AppColors.primary,
              fontColor: state.captchaCountdown > 0
                  ? AppColors.textColor3
                  : Colors.white,
              fontSize: 15,
              height: 35,
              onPressed:
                  state.captchaCountdown > 0 ? null : notifier.sendCaptchaCode,
              radius: 4,
            ),
          ],
        ),
        Divider(
          thickness: 1,
        ),
      ],
    );
  }

  /// 构建协议同意部分
  Widget _buildAgreementSection(
      TaxSupplementState state, TaxSupplementNotifier notifier) {
    return Row(
      children: [
        Checkbox(
          value: state.agreedToTerms,
          onChanged: (value) => notifier.updateAgreedToTerms(value ?? false),
          activeColor: const Color(0xFF4A90E2),
        ),
        const Text(
          '我已阅读并同意',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
        ),
        GestureDetector(
          onTap: () {
            // 打开授权协议
          },
          child: const Text(
            '《授权协议》',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF4A90E2),
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建下一步按钮
  Widget _buildNextButton(
      TaxSupplementState state, TaxSupplementNotifier notifier) {
    return buildFilledButton("下一步",
        onPressed: state.isLoading ? null : notifier.handleNextStep);
    // return SizedBox(
    //   width: double.infinity,
    //   height: 48,
    //   child: ElevatedButton(
    //     onPressed: state.isLoading ? null : notifier.handleNextStep,
    //     style: ElevatedButton.styleFrom(
    //       backgroundColor: const Color(0xFF4A90E2),
    //       foregroundColor: Colors.white,
    //       shape: RoundedRectangleBorder(
    //         borderRadius: BorderRadius.circular(24),
    //       ),
    //     ),
    //     child: state.isLoading
    //         ? const SizedBox(
    //             width: 20,
    //             height: 20,
    //             child: CircularProgressIndicator(
    //               strokeWidth: 2,
    //               valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
    //             ),
    //           )
    //         : const Text(
    //             '下一步',
    //             style: TextStyle(
    //               fontSize: 16,
    //               fontWeight: FontWeight.w500,
    //             ),
    //           ),
    //   ),
    // );
  }

  /// 构建成功页面
  Widget _buildSuccessPage(
      BuildContext context, TaxSupplementNotifier notifier) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text('补充资料'),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                AssetsImages.querySuccessPng,
                width: 200,
                height: 200,
              ),
              const SizedBox(height: 24),
              const Text(
                '正在查询匹配信息...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 12),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF666666),
                    height: 1.5,
                  ),
                  children: [
                    const TextSpan(text: '精准匹配计需要5分钟以上，您可以去页面'),
                    TextSpan(
                      text: '我的-匹配列表',
                      style: const TextStyle(
                        color: AppColors.primary,
                      ),
                      recognizer: TapGestureRecognizer()..onTap = () {},
                    ),
                    const TextSpan(text: '进行结果查看'),
                  ],
                ),
              ),
              const SizedBox(height: 48),

              buildFilledButton('返回首页', onPressed: () {
                Navigator.of(context).pop();
              }),
              // SizedBox(
              //   width: double.infinity,
              //   height: 48,
              //   child: ElevatedButton(
              //     onPressed: () {
              //       Navigator.of(context).popUntil((route) => route.isFirst);
              //     },
              //     style: ElevatedButton.styleFrom(
              //       backgroundColor: const Color(0xFF4A90E2),
              //       foregroundColor: Colors.white,
              //       shape: RoundedRectangleBorder(
              //         borderRadius: BorderRadius.circular(24),
              //       ),
              //     ),
              //     child: const Text(
              //       '返回首页',
              //       style: TextStyle(
              //         fontSize: 16,
              //         fontWeight: FontWeight.w500,
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
