// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tax_supplement_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$taxSupplementNotifierHash() =>
    r'66a3a471679d5bddc528629782aa1238ad435776';

/// 税务补充材料页面状态管理
///
/// Copied from [TaxSupplementNotifier].
@ProviderFor(TaxSupplementNotifier)
final taxSupplementNotifierProvider = AutoDisposeNotifierProvider<
    TaxSupplementNotifier, TaxSupplementState>.internal(
  TaxSupplementNotifier.new,
  name: r'taxSupplementNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$taxSupplementNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TaxSupplementNotifier = AutoDisposeNotifier<TaxSupplementState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
