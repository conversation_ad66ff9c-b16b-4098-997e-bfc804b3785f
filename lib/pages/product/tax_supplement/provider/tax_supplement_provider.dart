import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import '../models/tax_supplement_models.dart';

part 'tax_supplement_provider.g.dart';

/// 税务补充材料页面状态管理
@riverpod
class TaxSupplementNotifier extends _$TaxSupplementNotifier {
  Timer? _countdownTimer;

  @override
  TaxSupplementState build() {
    // 当provider被销毁时，自动清理定时器
    ref.onDispose(() {
      _countdownTimer?.cancel();
    });

    return const TaxSupplementState();
  }

  Province? _province;

  /// 初始化页面数据
  Future<void> initializeData({
    required String creditCode,
    required String enterpriseName,
    required String provinceId,
    required String personStand,
  }) async {
    // 根据省份ID获取省份名称
    final provinceNotifier = ref.read(provinceListNotifierProvider.notifier);
    try {
      await provinceNotifier.getProvinceList();
    } catch (e) {
      // 忽略省份列表获取失败的错误
    }
    final province = provinceNotifier.findProvinceById(provinceId);
    final provinceName = province?.pinyin ?? '';

    _province = province;

    state = state.copyWith(
      creditCode: creditCode,
      enterpriseName: enterpriseName,
      provinceId: provinceId,
      personStand: personStand,
      provinceName: provinceName,
    );
  }

  /// 更新电子税务局账号
  void updateTaxAccount(String account) {
    state = state.copyWith(taxAccount: account);
  }

  /// 更新税务密码
  void updateTaxPassword(String password) {
    state = state.copyWith(taxPassword: password);
  }

  /// 更新验证码
  void updateCaptchaCode(String code) {
    state = state.copyWith(captchaCode: code);
  }

  /// 更新协议同意状态
  void updateAgreedToTerms(bool agreed) {
    state = state.copyWith(agreedToTerms: agreed);
  }

  /// 检查是否需要验证码
  bool _needsCaptcha() {
    const noNeedSmsCodeAreas = ["zhejiang", "hubei", "guangdong"];
    return !noNeedSmsCodeAreas.contains(state.provinceName);
  }

  /// 处理下一步操作
  Future<void> handleNextStep() async {
    if (!_validateInputs()) {
      return;
    }

    state = state.copyWith(isLoading: true);

    Loading.show();

    try {
      if (_needsCaptcha()) {
        // 需要验证码的省份
        await _handleTaxLoginWithCaptcha();
      } else {
        // 不需要验证码的省份
        await _handleTaxOnlyAccountLogin();
      }
    } catch (e) {
      Loading.error('操作失败: $e');
    } finally {
      state = state.copyWith(isLoading: false);
      Loading.dismiss();
    }
  }

  /// 验证输入
  bool _validateInputs() {
    if (state.taxAccount.isEmpty) {
      Loading.error('请输入电子税务局账号');
      return false;
    }
    if (state.taxPassword.isEmpty) {
      Loading.error('请输入税务密码');
      return false;
    }
    if (!state.agreedToTerms) {
      Loading.error('请先同意《授权协议》');
      return false;
    }

    return true;
  }

  /// 处理不需要验证码的登录
  Future<void> _handleTaxOnlyAccountLogin() async {
    final entity = TaxOnlyAccountLoginEntity(
      queryChannel: '2',
      provinceName: state.provinceName,
      creditCode: state.creditCode,
      phone: state.taxAccount,
      password: state.taxPassword,
      provinceId: state.provinceId,
      taxpayerName: state.enterpriseName,
      personStand: state.personStand,
    );

    final result = await QueryApi.taxOnlyAccountLogin(entity: entity);
    // 登录成功，创建匹配企业
    state = state.copyWith(taxOnlyAccountLoginModel: result.data);
    await _createMatchEnterprise();
  }

  /// 处理需要验证码的登录
  Future<void> _handleTaxLoginWithCaptcha() async {
    if (state.taxLoginModel == null) {
      // 第一步：税务登录
      await _performTaxLogin();
    } else {
      // 第二步：验证码登录
      await _performCaptchaLogin();
    }
  }

  /// 执行税务登录（第一步）
  Future<void> _performTaxLogin() async {
    final entity = TaxNeedSmsCOdeLoginEntity(
      queryChannel: '2',
      creditCodeOrTaxPayerId: state.creditCode,
      phoneNumber: state.taxAccount,
      provinceName: state.provinceName,
      provinceId: state.provinceId,
      taxpayerName: state.enterpriseName,
      password: state.taxPassword,
      source: 'query',
      channelType: 'app',
      appid: 'wx3843eed9287d8daa',
    );

    final result = await QueryApi.taxLogin(entity: entity);
    if (result.data != null) {
      state = state.copyWith(
        taxLoginModel: result.data,
        needsCaptcha: true,
      );
    } else {
      throw Exception(result.message);
    }
  }

  /// 发送验证码
  Future<void> sendCaptchaCode() async {
    if (state.taxLoginModel == null) {
      Loading.error('请先完成账号密码验证');
      return;
    }

    if (state.captchaCountdown > 0) {
      Loading.error('请等待${state.captchaCountdown}秒后重新发送');
      return;
    }

    try {
      final entity = TaxSendSmsCodeEntity(
        phone: state.taxAccount,
        creditCodeOrTaxPayerId: state.creditCode,
        uuid: state.taxLoginModel!.uuid,
      );

      final result = await QueryApi.taxSendSmsCode(entity: entity);
      if (result.code == 200) {
        _startCountdown();
        // Loading.success('验证码发送成功'); // Loading没有success方法，使用toast或其他方式
      } else {
        throw Exception(result.message);
      }
    } catch (e) {
      Loading.error('发送验证码失败: $e');
    }
  }

  /// 开始倒计时
  void _startCountdown() {
    state = state.copyWith(captchaCountdown: 60);
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.captchaCountdown > 0) {
        state = state.copyWith(captchaCountdown: state.captchaCountdown - 1);
      } else {
        timer.cancel();
      }
    });
  }

  /// 执行验证码登录
  Future<void> _performCaptchaLogin() async {
    if (state.taxLoginModel == null) {
      throw Exception('缺少登录信息');
    }

    if (state.captchaCode.isEmpty) {
      throw Exception('请输入验证码');
    }

    final entity = TaxCaptchaLoginEntity(
      provinceName: state.provinceName,
      creditCodeOrTaxPayerId: state.creditCode,
      phoneNumber: state.taxAccount,
      password: state.taxPassword,
      code: state.captchaCode,
      enterpriseId: state.taxLoginModel!.enterpriseId,
      uuid: state.taxLoginModel!.uuid,
    );

    final result = await QueryApi.taxCaptchaLogin(entity: entity);
    if (result.code == 200) {
      // 验证码登录成功，创建匹配企业
      await _createMatchEnterprise();
    } else {
      throw Exception(result.message);
    }
  }

  /// 创建匹配企业
  Future<void> _createMatchEnterprise() async {
    try {
      // 获取taskId和enterpriseId，这些通常来自之前的登录响应
      String taskId = (_needsCaptcha()
              ? state.taxLoginModel?.queryTaskId
              : state.taxOnlyAccountLoginModel?.id) ??
          '';
      String enterpriseId = (_needsCaptcha()
              ? state.taxLoginModel?.enterpriseId
              : state.taxOnlyAccountLoginModel?.enterpriseId) ??
          '';

      final result = await QueryApi.createMatchEnterprise(
        taskId: taskId,
        enterpriseId: enterpriseId,
        provinceName: _province?.name ?? '',
        creditCode: state.creditCode,
        phone: state.taxAccount,
        creditPwd: state.taxPassword,
        areaId: _province?.areaId ?? '',
        enterpriseName: state.enterpriseName,
        personStand: state.personStand,
      );
      state = state.copyWith(showSuccessPage: true);
    } catch (e) {
      Loading.error('$e');
    }
  }

  /// 返回首页
  void goToHomePage() {
    // 这里应该导航到首页
    // 具体实现需要在页面中处理
  }
}
