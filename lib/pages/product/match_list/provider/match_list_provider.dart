import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../api/match_list_api.dart';
import '../models/index.dart';

part 'match_list_provider.g.dart';
part 'match_list_provider.freezed.dart';

/// 匹配列表页面状态
@freezed
abstract class MatchListState with _$MatchListState {
  const factory MatchListState({
    /// 当前选中的匹配类型
    @Default(MatchType.all) MatchType selectedMatchType,

    /// 搜索关键词
    @Default('') String searchKeyword,

    /// 是否正在加载
    @Default(false) bool isLoading,

    /// 是否正在加载更多
    @Default(false) bool isLoadingMore,

    /// 错误信息
    String? errorMessage,

    /// 匹配企业列表
    @Default([]) List<MatchEnterprise> enterprises,

    /// 当前页码
    @Default(1) int currentPage,

    /// 每页大小
    @Default(10) int pageSize,

    /// 是否还有更多数据
    @Default(true) bool hasMore,

    /// 总数量
    @Default(0) int total,
  }) = _MatchListState;
}

/// 匹配列表状态管理
@riverpod
class MatchListNotifier extends _$MatchListNotifier {
  CancelToken? _cancelToken;

  @override
  MatchListState build() {
    // 页面销毁时取消请求
    ref.onDispose(() {
      _cancelToken?.cancel();
    });

    return const MatchListState();
  }

  /// 初始化加载数据
  Future<void> initialize() async {
    await _loadFirstPage();
  }

  /// 切换匹配类型
  Future<void> switchMatchType(MatchType matchType) async {
    if (state.selectedMatchType == matchType) return;

    state = state.copyWith(
      selectedMatchType: matchType,
      currentPage: 1,
      enterprises: [],
      hasMore: true,
    );

    await _loadFirstPage();
  }

  /// 更新搜索关键词
  Future<void> updateSearchKeyword(String keyword) async {
    if (state.searchKeyword == keyword) return;

    state = state.copyWith(
      searchKeyword: keyword,
      currentPage: 1,
      enterprises: [],
      hasMore: true,
    );

    await _loadFirstPage();
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadFirstPage();
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    state = state.copyWith(isLoadingMore: true);

    try {
      await _loadData(state.currentPage + 1, isLoadMore: true);
    } catch (e) {
      // 加载更多失败不显示错误，只是停止加载状态
      state = state.copyWith(isLoadingMore: false);
    }
  }

  /// 加载第一页数据
  Future<void> _loadFirstPage() async {
    if (state.enterprises.isEmpty) {
      state = state.copyWith(
        isLoading: true,
        errorMessage: null,
      );
    }

    try {
      await _loadData(1);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 加载数据的通用方法
  Future<void> _loadData(int page, {bool isLoadMore = false}) async {
    _cancelToken?.cancel();
    _cancelToken = CancelToken();

    final query = MatchEnterpriseQuery(
      pageNum: page,
      pageSize: state.pageSize,
      enterpriseName: state.searchKeyword,
      matchStep: state.selectedMatchType.value,
    );

    final response = await MatchListApi.getMatchEnterpriseList(
      query: query,
      cancelToken: _cancelToken,
    );

    if (response.code == 200 && response.data != null) {
      final pagination = response.data!;
      final newEnterprises = pagination.list;

      List<MatchEnterprise> allEnterprises;
      if (isLoadMore) {
        // 加载更多：追加到现有列表
        allEnterprises = [...state.enterprises, ...newEnterprises];
      } else {
        // 首次加载或刷新：替换列表
        allEnterprises = newEnterprises;
      }

      state = state.copyWith(
        enterprises: allEnterprises,
        currentPage: pagination.pageNum,
        total: pagination.total,
        hasMore: pagination.hasMorePage,
        isLoading: false,
        isLoadingMore: false,
        errorMessage: null,
      );
    } else {
      throw Exception(response.message.isNotEmpty ? response.message : '加载失败');
    }
  }
}
