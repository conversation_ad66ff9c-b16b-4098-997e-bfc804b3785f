// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_list_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchListState {
  /// 当前选中的匹配类型
  MatchType get selectedMatchType;

  /// 搜索关键词
  String get searchKeyword;

  /// 是否正在加载
  bool get isLoading;

  /// 是否正在加载更多
  bool get isLoadingMore;

  /// 错误信息
  String? get errorMessage;

  /// 匹配企业列表
  List<MatchEnterprise> get enterprises;

  /// 当前页码
  int get currentPage;

  /// 每页大小
  int get pageSize;

  /// 是否还有更多数据
  bool get hasMore;

  /// 总数量
  int get total;

  /// Create a copy of MatchListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchListStateCopyWith<MatchListState> get copyWith =>
      _$MatchListStateCopyWithImpl<MatchListState>(
          this as MatchListState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchListState &&
            (identical(other.selectedMatchType, selectedMatchType) ||
                other.selectedMatchType == selectedMatchType) &&
            (identical(other.searchKeyword, searchKeyword) ||
                other.searchKeyword == searchKeyword) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other.enterprises, enterprises) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.total, total) || other.total == total));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedMatchType,
      searchKeyword,
      isLoading,
      isLoadingMore,
      errorMessage,
      const DeepCollectionEquality().hash(enterprises),
      currentPage,
      pageSize,
      hasMore,
      total);

  @override
  String toString() {
    return 'MatchListState(selectedMatchType: $selectedMatchType, searchKeyword: $searchKeyword, isLoading: $isLoading, isLoadingMore: $isLoadingMore, errorMessage: $errorMessage, enterprises: $enterprises, currentPage: $currentPage, pageSize: $pageSize, hasMore: $hasMore, total: $total)';
  }
}

/// @nodoc
abstract mixin class $MatchListStateCopyWith<$Res> {
  factory $MatchListStateCopyWith(
          MatchListState value, $Res Function(MatchListState) _then) =
      _$MatchListStateCopyWithImpl;
  @useResult
  $Res call(
      {MatchType selectedMatchType,
      String searchKeyword,
      bool isLoading,
      bool isLoadingMore,
      String? errorMessage,
      List<MatchEnterprise> enterprises,
      int currentPage,
      int pageSize,
      bool hasMore,
      int total});
}

/// @nodoc
class _$MatchListStateCopyWithImpl<$Res>
    implements $MatchListStateCopyWith<$Res> {
  _$MatchListStateCopyWithImpl(this._self, this._then);

  final MatchListState _self;
  final $Res Function(MatchListState) _then;

  /// Create a copy of MatchListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedMatchType = null,
    Object? searchKeyword = null,
    Object? isLoading = null,
    Object? isLoadingMore = null,
    Object? errorMessage = freezed,
    Object? enterprises = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? hasMore = null,
    Object? total = null,
  }) {
    return _then(_self.copyWith(
      selectedMatchType: null == selectedMatchType
          ? _self.selectedMatchType
          : selectedMatchType // ignore: cast_nullable_to_non_nullable
              as MatchType,
      searchKeyword: null == searchKeyword
          ? _self.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _self.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      enterprises: null == enterprises
          ? _self.enterprises
          : enterprises // ignore: cast_nullable_to_non_nullable
              as List<MatchEnterprise>,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      total: null == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _MatchListState implements MatchListState {
  const _MatchListState(
      {this.selectedMatchType = MatchType.all,
      this.searchKeyword = '',
      this.isLoading = false,
      this.isLoadingMore = false,
      this.errorMessage,
      final List<MatchEnterprise> enterprises = const [],
      this.currentPage = 1,
      this.pageSize = 10,
      this.hasMore = true,
      this.total = 0})
      : _enterprises = enterprises;

  /// 当前选中的匹配类型
  @override
  @JsonKey()
  final MatchType selectedMatchType;

  /// 搜索关键词
  @override
  @JsonKey()
  final String searchKeyword;

  /// 是否正在加载
  @override
  @JsonKey()
  final bool isLoading;

  /// 是否正在加载更多
  @override
  @JsonKey()
  final bool isLoadingMore;

  /// 错误信息
  @override
  final String? errorMessage;

  /// 匹配企业列表
  final List<MatchEnterprise> _enterprises;

  /// 匹配企业列表
  @override
  @JsonKey()
  List<MatchEnterprise> get enterprises {
    if (_enterprises is EqualUnmodifiableListView) return _enterprises;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_enterprises);
  }

  /// 当前页码
  @override
  @JsonKey()
  final int currentPage;

  /// 每页大小
  @override
  @JsonKey()
  final int pageSize;

  /// 是否还有更多数据
  @override
  @JsonKey()
  final bool hasMore;

  /// 总数量
  @override
  @JsonKey()
  final int total;

  /// Create a copy of MatchListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchListStateCopyWith<_MatchListState> get copyWith =>
      __$MatchListStateCopyWithImpl<_MatchListState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchListState &&
            (identical(other.selectedMatchType, selectedMatchType) ||
                other.selectedMatchType == selectedMatchType) &&
            (identical(other.searchKeyword, searchKeyword) ||
                other.searchKeyword == searchKeyword) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._enterprises, _enterprises) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.total, total) || other.total == total));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedMatchType,
      searchKeyword,
      isLoading,
      isLoadingMore,
      errorMessage,
      const DeepCollectionEquality().hash(_enterprises),
      currentPage,
      pageSize,
      hasMore,
      total);

  @override
  String toString() {
    return 'MatchListState(selectedMatchType: $selectedMatchType, searchKeyword: $searchKeyword, isLoading: $isLoading, isLoadingMore: $isLoadingMore, errorMessage: $errorMessage, enterprises: $enterprises, currentPage: $currentPage, pageSize: $pageSize, hasMore: $hasMore, total: $total)';
  }
}

/// @nodoc
abstract mixin class _$MatchListStateCopyWith<$Res>
    implements $MatchListStateCopyWith<$Res> {
  factory _$MatchListStateCopyWith(
          _MatchListState value, $Res Function(_MatchListState) _then) =
      __$MatchListStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {MatchType selectedMatchType,
      String searchKeyword,
      bool isLoading,
      bool isLoadingMore,
      String? errorMessage,
      List<MatchEnterprise> enterprises,
      int currentPage,
      int pageSize,
      bool hasMore,
      int total});
}

/// @nodoc
class __$MatchListStateCopyWithImpl<$Res>
    implements _$MatchListStateCopyWith<$Res> {
  __$MatchListStateCopyWithImpl(this._self, this._then);

  final _MatchListState _self;
  final $Res Function(_MatchListState) _then;

  /// Create a copy of MatchListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedMatchType = null,
    Object? searchKeyword = null,
    Object? isLoading = null,
    Object? isLoadingMore = null,
    Object? errorMessage = freezed,
    Object? enterprises = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? hasMore = null,
    Object? total = null,
  }) {
    return _then(_MatchListState(
      selectedMatchType: null == selectedMatchType
          ? _self.selectedMatchType
          : selectedMatchType // ignore: cast_nullable_to_non_nullable
              as MatchType,
      searchKeyword: null == searchKeyword
          ? _self.searchKeyword
          : searchKeyword // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingMore: null == isLoadingMore
          ? _self.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      enterprises: null == enterprises
          ? _self._enterprises
          : enterprises // ignore: cast_nullable_to_non_nullable
              as List<MatchEnterprise>,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      total: null == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
