import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_enterprise.freezed.dart';
part 'match_enterprise.g.dart';

/// 匹配企业数据模型
@freezed
abstract class MatchEnterprise with _$MatchEnterprise {
  const factory MatchEnterprise({
    /// 企业ID
    required String id,
    /// 类型
    required int type,
    /// 企业ID（可能为空）
    String? enterpriseId,
    /// 企业名称
    required String enterpriseName,
    /// 地址
    String? addr,
    /// 法人代表
    String? legalPerson,
    /// 统一社会信用代码
    String? creditCode,
    /// 联系电话
    String? phone,
    /// 省份名称
    String? provinceName,
    /// 创建时间
    String? createTime,
    /// 更新时间
    String? updateTime,
    /// 匹配状态
    required int matchStatus,
    /// 产品数量
    required int productNum,
    /// 匹配步骤 (0=答题匹配, 1=税务匹配)
    required int matchStep,
    /// 是否收藏
    @Default(false) bool collect,
  }) = _MatchEnterprise;

  factory MatchEnterprise.fromJson(Map<String, dynamic> json) =>
      _$MatchEnterpriseFromJson(json);
}

/// 匹配企业列表查询参数
@freezed
abstract class MatchEnterpriseQuery with _$MatchEnterpriseQuery {
  const factory MatchEnterpriseQuery({
    /// 页码
    @Default(1) int pageNum,
    /// 每页大小
    @Default(10) int pageSize,
    /// 企业名称（搜索关键词）
    @Default('') String enterpriseName,
    /// 匹配步骤 (空=全部, 0=答题匹配, 1=税务匹配)
    String? matchStep,
  }) = _MatchEnterpriseQuery;

  factory MatchEnterpriseQuery.fromJson(Map<String, dynamic> json) =>
      _$MatchEnterpriseQueryFromJson(json);
}

/// 匹配类型枚举
enum MatchType {
  /// 全部
  all('全部', null),
  /// 答题匹配
  quiz('答题匹配', '0'),
  /// 税务匹配
  tax('税务匹配', '1');

  const MatchType(this.displayName, this.value);
  
  /// 显示名称
  final String displayName;
  /// API参数值
  final String? value;
}
