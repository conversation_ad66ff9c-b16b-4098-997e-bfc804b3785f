// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_enterprise.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchEnterprise {
  /// 企业ID
  String get id;

  /// 类型
  int get type;

  /// 企业ID（可能为空）
  String? get enterpriseId;

  /// 企业名称
  String get enterpriseName;

  /// 地址
  String? get addr;

  /// 法人代表
  String? get legalPerson;

  /// 统一社会信用代码
  String? get creditCode;

  /// 联系电话
  String? get phone;

  /// 省份名称
  String? get provinceName;

  /// 创建时间
  String? get createTime;

  /// 更新时间
  String? get updateTime;

  /// 匹配状态
  int get matchStatus;

  /// 产品数量
  int get productNum;

  /// 匹配步骤 (0=答题匹配, 1=税务匹配)
  int get matchStep;

  /// 是否收藏
  bool get collect;

  /// Create a copy of MatchEnterprise
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchEnterpriseCopyWith<MatchEnterprise> get copyWith =>
      _$MatchEnterpriseCopyWithImpl<MatchEnterprise>(
          this as MatchEnterprise, _$identity);

  /// Serializes this MatchEnterprise to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchEnterprise &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.addr, addr) || other.addr == addr) &&
            (identical(other.legalPerson, legalPerson) ||
                other.legalPerson == legalPerson) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.provinceName, provinceName) ||
                other.provinceName == provinceName) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus) &&
            (identical(other.productNum, productNum) ||
                other.productNum == productNum) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.collect, collect) || other.collect == collect));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      type,
      enterpriseId,
      enterpriseName,
      addr,
      legalPerson,
      creditCode,
      phone,
      provinceName,
      createTime,
      updateTime,
      matchStatus,
      productNum,
      matchStep,
      collect);

  @override
  String toString() {
    return 'MatchEnterprise(id: $id, type: $type, enterpriseId: $enterpriseId, enterpriseName: $enterpriseName, addr: $addr, legalPerson: $legalPerson, creditCode: $creditCode, phone: $phone, provinceName: $provinceName, createTime: $createTime, updateTime: $updateTime, matchStatus: $matchStatus, productNum: $productNum, matchStep: $matchStep, collect: $collect)';
  }
}

/// @nodoc
abstract mixin class $MatchEnterpriseCopyWith<$Res> {
  factory $MatchEnterpriseCopyWith(
          MatchEnterprise value, $Res Function(MatchEnterprise) _then) =
      _$MatchEnterpriseCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      int type,
      String? enterpriseId,
      String enterpriseName,
      String? addr,
      String? legalPerson,
      String? creditCode,
      String? phone,
      String? provinceName,
      String? createTime,
      String? updateTime,
      int matchStatus,
      int productNum,
      int matchStep,
      bool collect});
}

/// @nodoc
class _$MatchEnterpriseCopyWithImpl<$Res>
    implements $MatchEnterpriseCopyWith<$Res> {
  _$MatchEnterpriseCopyWithImpl(this._self, this._then);

  final MatchEnterprise _self;
  final $Res Function(MatchEnterprise) _then;

  /// Create a copy of MatchEnterprise
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? enterpriseId = freezed,
    Object? enterpriseName = null,
    Object? addr = freezed,
    Object? legalPerson = freezed,
    Object? creditCode = freezed,
    Object? phone = freezed,
    Object? provinceName = freezed,
    Object? createTime = freezed,
    Object? updateTime = freezed,
    Object? matchStatus = null,
    Object? productNum = null,
    Object? matchStep = null,
    Object? collect = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      addr: freezed == addr
          ? _self.addr
          : addr // ignore: cast_nullable_to_non_nullable
              as String?,
      legalPerson: freezed == legalPerson
          ? _self.legalPerson
          : legalPerson // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      provinceName: freezed == provinceName
          ? _self.provinceName
          : provinceName // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateTime: freezed == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
      productNum: null == productNum
          ? _self.productNum
          : productNum // ignore: cast_nullable_to_non_nullable
              as int,
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      collect: null == collect
          ? _self.collect
          : collect // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchEnterprise implements MatchEnterprise {
  const _MatchEnterprise(
      {required this.id,
      required this.type,
      this.enterpriseId,
      required this.enterpriseName,
      this.addr,
      this.legalPerson,
      this.creditCode,
      this.phone,
      this.provinceName,
      this.createTime,
      this.updateTime,
      required this.matchStatus,
      required this.productNum,
      required this.matchStep,
      this.collect = false});
  factory _MatchEnterprise.fromJson(Map<String, dynamic> json) =>
      _$MatchEnterpriseFromJson(json);

  /// 企业ID
  @override
  final String id;

  /// 类型
  @override
  final int type;

  /// 企业ID（可能为空）
  @override
  final String? enterpriseId;

  /// 企业名称
  @override
  final String enterpriseName;

  /// 地址
  @override
  final String? addr;

  /// 法人代表
  @override
  final String? legalPerson;

  /// 统一社会信用代码
  @override
  final String? creditCode;

  /// 联系电话
  @override
  final String? phone;

  /// 省份名称
  @override
  final String? provinceName;

  /// 创建时间
  @override
  final String? createTime;

  /// 更新时间
  @override
  final String? updateTime;

  /// 匹配状态
  @override
  final int matchStatus;

  /// 产品数量
  @override
  final int productNum;

  /// 匹配步骤 (0=答题匹配, 1=税务匹配)
  @override
  final int matchStep;

  /// 是否收藏
  @override
  @JsonKey()
  final bool collect;

  /// Create a copy of MatchEnterprise
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchEnterpriseCopyWith<_MatchEnterprise> get copyWith =>
      __$MatchEnterpriseCopyWithImpl<_MatchEnterprise>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchEnterpriseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchEnterprise &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.addr, addr) || other.addr == addr) &&
            (identical(other.legalPerson, legalPerson) ||
                other.legalPerson == legalPerson) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.provinceName, provinceName) ||
                other.provinceName == provinceName) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus) &&
            (identical(other.productNum, productNum) ||
                other.productNum == productNum) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.collect, collect) || other.collect == collect));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      type,
      enterpriseId,
      enterpriseName,
      addr,
      legalPerson,
      creditCode,
      phone,
      provinceName,
      createTime,
      updateTime,
      matchStatus,
      productNum,
      matchStep,
      collect);

  @override
  String toString() {
    return 'MatchEnterprise(id: $id, type: $type, enterpriseId: $enterpriseId, enterpriseName: $enterpriseName, addr: $addr, legalPerson: $legalPerson, creditCode: $creditCode, phone: $phone, provinceName: $provinceName, createTime: $createTime, updateTime: $updateTime, matchStatus: $matchStatus, productNum: $productNum, matchStep: $matchStep, collect: $collect)';
  }
}

/// @nodoc
abstract mixin class _$MatchEnterpriseCopyWith<$Res>
    implements $MatchEnterpriseCopyWith<$Res> {
  factory _$MatchEnterpriseCopyWith(
          _MatchEnterprise value, $Res Function(_MatchEnterprise) _then) =
      __$MatchEnterpriseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      int type,
      String? enterpriseId,
      String enterpriseName,
      String? addr,
      String? legalPerson,
      String? creditCode,
      String? phone,
      String? provinceName,
      String? createTime,
      String? updateTime,
      int matchStatus,
      int productNum,
      int matchStep,
      bool collect});
}

/// @nodoc
class __$MatchEnterpriseCopyWithImpl<$Res>
    implements _$MatchEnterpriseCopyWith<$Res> {
  __$MatchEnterpriseCopyWithImpl(this._self, this._then);

  final _MatchEnterprise _self;
  final $Res Function(_MatchEnterprise) _then;

  /// Create a copy of MatchEnterprise
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? enterpriseId = freezed,
    Object? enterpriseName = null,
    Object? addr = freezed,
    Object? legalPerson = freezed,
    Object? creditCode = freezed,
    Object? phone = freezed,
    Object? provinceName = freezed,
    Object? createTime = freezed,
    Object? updateTime = freezed,
    Object? matchStatus = null,
    Object? productNum = null,
    Object? matchStep = null,
    Object? collect = null,
  }) {
    return _then(_MatchEnterprise(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      addr: freezed == addr
          ? _self.addr
          : addr // ignore: cast_nullable_to_non_nullable
              as String?,
      legalPerson: freezed == legalPerson
          ? _self.legalPerson
          : legalPerson // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _self.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      provinceName: freezed == provinceName
          ? _self.provinceName
          : provinceName // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateTime: freezed == updateTime
          ? _self.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
      productNum: null == productNum
          ? _self.productNum
          : productNum // ignore: cast_nullable_to_non_nullable
              as int,
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      collect: null == collect
          ? _self.collect
          : collect // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$MatchEnterpriseQuery {
  /// 页码
  int get pageNum;

  /// 每页大小
  int get pageSize;

  /// 企业名称（搜索关键词）
  String get enterpriseName;

  /// 匹配步骤 (空=全部, 0=答题匹配, 1=税务匹配)
  String? get matchStep;

  /// Create a copy of MatchEnterpriseQuery
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchEnterpriseQueryCopyWith<MatchEnterpriseQuery> get copyWith =>
      _$MatchEnterpriseQueryCopyWithImpl<MatchEnterpriseQuery>(
          this as MatchEnterpriseQuery, _$identity);

  /// Serializes this MatchEnterpriseQuery to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchEnterpriseQuery &&
            (identical(other.pageNum, pageNum) || other.pageNum == pageNum) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pageNum, pageSize, enterpriseName, matchStep);

  @override
  String toString() {
    return 'MatchEnterpriseQuery(pageNum: $pageNum, pageSize: $pageSize, enterpriseName: $enterpriseName, matchStep: $matchStep)';
  }
}

/// @nodoc
abstract mixin class $MatchEnterpriseQueryCopyWith<$Res> {
  factory $MatchEnterpriseQueryCopyWith(MatchEnterpriseQuery value,
          $Res Function(MatchEnterpriseQuery) _then) =
      _$MatchEnterpriseQueryCopyWithImpl;
  @useResult
  $Res call(
      {int pageNum, int pageSize, String enterpriseName, String? matchStep});
}

/// @nodoc
class _$MatchEnterpriseQueryCopyWithImpl<$Res>
    implements $MatchEnterpriseQueryCopyWith<$Res> {
  _$MatchEnterpriseQueryCopyWithImpl(this._self, this._then);

  final MatchEnterpriseQuery _self;
  final $Res Function(MatchEnterpriseQuery) _then;

  /// Create a copy of MatchEnterpriseQuery
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageNum = null,
    Object? pageSize = null,
    Object? enterpriseName = null,
    Object? matchStep = freezed,
  }) {
    return _then(_self.copyWith(
      pageNum: null == pageNum
          ? _self.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      matchStep: freezed == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchEnterpriseQuery implements MatchEnterpriseQuery {
  const _MatchEnterpriseQuery(
      {this.pageNum = 1,
      this.pageSize = 10,
      this.enterpriseName = '',
      this.matchStep});
  factory _MatchEnterpriseQuery.fromJson(Map<String, dynamic> json) =>
      _$MatchEnterpriseQueryFromJson(json);

  /// 页码
  @override
  @JsonKey()
  final int pageNum;

  /// 每页大小
  @override
  @JsonKey()
  final int pageSize;

  /// 企业名称（搜索关键词）
  @override
  @JsonKey()
  final String enterpriseName;

  /// 匹配步骤 (空=全部, 0=答题匹配, 1=税务匹配)
  @override
  final String? matchStep;

  /// Create a copy of MatchEnterpriseQuery
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchEnterpriseQueryCopyWith<_MatchEnterpriseQuery> get copyWith =>
      __$MatchEnterpriseQueryCopyWithImpl<_MatchEnterpriseQuery>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchEnterpriseQueryToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchEnterpriseQuery &&
            (identical(other.pageNum, pageNum) || other.pageNum == pageNum) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pageNum, pageSize, enterpriseName, matchStep);

  @override
  String toString() {
    return 'MatchEnterpriseQuery(pageNum: $pageNum, pageSize: $pageSize, enterpriseName: $enterpriseName, matchStep: $matchStep)';
  }
}

/// @nodoc
abstract mixin class _$MatchEnterpriseQueryCopyWith<$Res>
    implements $MatchEnterpriseQueryCopyWith<$Res> {
  factory _$MatchEnterpriseQueryCopyWith(_MatchEnterpriseQuery value,
          $Res Function(_MatchEnterpriseQuery) _then) =
      __$MatchEnterpriseQueryCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int pageNum, int pageSize, String enterpriseName, String? matchStep});
}

/// @nodoc
class __$MatchEnterpriseQueryCopyWithImpl<$Res>
    implements _$MatchEnterpriseQueryCopyWith<$Res> {
  __$MatchEnterpriseQueryCopyWithImpl(this._self, this._then);

  final _MatchEnterpriseQuery _self;
  final $Res Function(_MatchEnterpriseQuery) _then;

  /// Create a copy of MatchEnterpriseQuery
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? pageNum = null,
    Object? pageSize = null,
    Object? enterpriseName = null,
    Object? matchStep = freezed,
  }) {
    return _then(_MatchEnterpriseQuery(
      pageNum: null == pageNum
          ? _self.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _self.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      matchStep: freezed == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
