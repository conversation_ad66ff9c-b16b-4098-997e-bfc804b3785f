import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import '../models/quiz_preparation_models.dart';

part 'quiz_preparation_service.g.dart';

/// 答题准备服务
@riverpod
QuizPreparationService quizPreparationService(Ref ref) {
  return QuizPreparationService(ref);
}

/// 答题准备服务实现
class QuizPreparationService {
  final Ref ref;

  QuizPreparationService(this.ref);

  /// 创建问题匹配
  Future<QuizPreparationResponse> createQuestionMatch(
    QuizPreparationRequest request,
  ) async {
    try {
      final response = await SXHttpService.to.post(
        '/cmsMatchEnterprise/create/questionMatch',
        data: request.toJson(),
      );

      return QuizPreparationResponse.fromJson(
        response.data,
        (data) => QuizPreparationData.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('创建问题匹配失败: $e');
    }
  }

  /// 创建问题匹配
  Future<BaseResponse<int?>> checkMatchCount() async {
    try {
      final response = await SXHttpService.to.get(
        '/match/checkMatchCount',
      );
      return createIntBaseResponse(response.data);
    } catch (e) {
      throw Exception('创建问题匹配失败: $e');
    }
  }
}
