// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_preparation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$quizPreparationNotifierHash() =>
    r'f877937ebbb3860171caa2711dd0f3efa50e0988';

/// 答题准备页面状态管理
///
/// Copied from [QuizPreparationNotifier].
@ProviderFor(QuizPreparationNotifier)
final quizPreparationNotifierProvider = AutoDisposeNotifierProvider<
    QuizPreparationNotifier, QuizPreparationState>.internal(
  QuizPreparationNotifier.new,
  name: r'quizPreparationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$quizPreparationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QuizPreparationNotifier = AutoDisposeNotifier<QuizPreparationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
