// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_preparation_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuizPreparationParams {
  /// 统一社会信用代码
  String get creditCode;

  /// 企业名称
  String get enterpriseName;

  /// 申请人身份
  String get personStand;

  /// 年龄
  String get age;

  /// 地区ID
  String get areaId;

  /// 渠道类型
  String get channelType;

  /// Create a copy of QuizPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizPreparationParamsCopyWith<QuizPreparationParams> get copyWith =>
      _$QuizPreparationParamsCopyWithImpl<QuizPreparationParams>(
          this as QuizPreparationParams, _$identity);

  /// Serializes this QuizPreparationParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizPreparationParams &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, creditCode, enterpriseName,
      personStand, age, areaId, channelType);

  @override
  String toString() {
    return 'QuizPreparationParams(creditCode: $creditCode, enterpriseName: $enterpriseName, personStand: $personStand, age: $age, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class $QuizPreparationParamsCopyWith<$Res> {
  factory $QuizPreparationParamsCopyWith(QuizPreparationParams value,
          $Res Function(QuizPreparationParams) _then) =
      _$QuizPreparationParamsCopyWithImpl;
  @useResult
  $Res call(
      {String creditCode,
      String enterpriseName,
      String personStand,
      String age,
      String areaId,
      String channelType});
}

/// @nodoc
class _$QuizPreparationParamsCopyWithImpl<$Res>
    implements $QuizPreparationParamsCopyWith<$Res> {
  _$QuizPreparationParamsCopyWithImpl(this._self, this._then);

  final QuizPreparationParams _self;
  final $Res Function(QuizPreparationParams) _then;

  /// Create a copy of QuizPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? personStand = null,
    Object? age = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_self.copyWith(
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuizPreparationParams implements QuizPreparationParams {
  const _QuizPreparationParams(
      {required this.creditCode,
      required this.enterpriseName,
      required this.personStand,
      required this.age,
      required this.areaId,
      required this.channelType});
  factory _QuizPreparationParams.fromJson(Map<String, dynamic> json) =>
      _$QuizPreparationParamsFromJson(json);

  /// 统一社会信用代码
  @override
  final String creditCode;

  /// 企业名称
  @override
  final String enterpriseName;

  /// 申请人身份
  @override
  final String personStand;

  /// 年龄
  @override
  final String age;

  /// 地区ID
  @override
  final String areaId;

  /// 渠道类型
  @override
  final String channelType;

  /// Create a copy of QuizPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizPreparationParamsCopyWith<_QuizPreparationParams> get copyWith =>
      __$QuizPreparationParamsCopyWithImpl<_QuizPreparationParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuizPreparationParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizPreparationParams &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, creditCode, enterpriseName,
      personStand, age, areaId, channelType);

  @override
  String toString() {
    return 'QuizPreparationParams(creditCode: $creditCode, enterpriseName: $enterpriseName, personStand: $personStand, age: $age, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class _$QuizPreparationParamsCopyWith<$Res>
    implements $QuizPreparationParamsCopyWith<$Res> {
  factory _$QuizPreparationParamsCopyWith(_QuizPreparationParams value,
          $Res Function(_QuizPreparationParams) _then) =
      __$QuizPreparationParamsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String creditCode,
      String enterpriseName,
      String personStand,
      String age,
      String areaId,
      String channelType});
}

/// @nodoc
class __$QuizPreparationParamsCopyWithImpl<$Res>
    implements _$QuizPreparationParamsCopyWith<$Res> {
  __$QuizPreparationParamsCopyWithImpl(this._self, this._then);

  final _QuizPreparationParams _self;
  final $Res Function(_QuizPreparationParams) _then;

  /// Create a copy of QuizPreparationParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? personStand = null,
    Object? age = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_QuizPreparationParams(
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$QuizPreparationRequest {
  /// 匹配步骤，默认为0
  int get matchStep;

  /// 申请人身份 1
  String get personStand;

  /// 年龄
  String get age;

  /// 统一社会信用代码
  String get creditCode;

  /// 企业名称
  String get enterpriseName;

  /// 地区ID
  String get areaId;

  /// 渠道类型，默认为routine
  String get channelType;

  /// Create a copy of QuizPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizPreparationRequestCopyWith<QuizPreparationRequest> get copyWith =>
      _$QuizPreparationRequestCopyWithImpl<QuizPreparationRequest>(
          this as QuizPreparationRequest, _$identity);

  /// Serializes this QuizPreparationRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizPreparationRequest &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchStep, personStand, age,
      creditCode, enterpriseName, areaId, channelType);

  @override
  String toString() {
    return 'QuizPreparationRequest(matchStep: $matchStep, personStand: $personStand, age: $age, creditCode: $creditCode, enterpriseName: $enterpriseName, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class $QuizPreparationRequestCopyWith<$Res> {
  factory $QuizPreparationRequestCopyWith(QuizPreparationRequest value,
          $Res Function(QuizPreparationRequest) _then) =
      _$QuizPreparationRequestCopyWithImpl;
  @useResult
  $Res call(
      {int matchStep,
      String personStand,
      String age,
      String creditCode,
      String enterpriseName,
      String areaId,
      String channelType});
}

/// @nodoc
class _$QuizPreparationRequestCopyWithImpl<$Res>
    implements $QuizPreparationRequestCopyWith<$Res> {
  _$QuizPreparationRequestCopyWithImpl(this._self, this._then);

  final QuizPreparationRequest _self;
  final $Res Function(QuizPreparationRequest) _then;

  /// Create a copy of QuizPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchStep = null,
    Object? personStand = null,
    Object? age = null,
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_self.copyWith(
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuizPreparationRequest implements QuizPreparationRequest {
  const _QuizPreparationRequest(
      {this.matchStep = 0,
      required this.personStand,
      required this.age,
      required this.creditCode,
      required this.enterpriseName,
      required this.areaId,
      this.channelType = "routine"});
  factory _QuizPreparationRequest.fromJson(Map<String, dynamic> json) =>
      _$QuizPreparationRequestFromJson(json);

  /// 匹配步骤，默认为0
  @override
  @JsonKey()
  final int matchStep;

  /// 申请人身份 1
  @override
  final String personStand;

  /// 年龄
  @override
  final String age;

  /// 统一社会信用代码
  @override
  final String creditCode;

  /// 企业名称
  @override
  final String enterpriseName;

  /// 地区ID
  @override
  final String areaId;

  /// 渠道类型，默认为routine
  @override
  @JsonKey()
  final String channelType;

  /// Create a copy of QuizPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizPreparationRequestCopyWith<_QuizPreparationRequest> get copyWith =>
      __$QuizPreparationRequestCopyWithImpl<_QuizPreparationRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuizPreparationRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizPreparationRequest &&
            (identical(other.matchStep, matchStep) ||
                other.matchStep == matchStep) &&
            (identical(other.personStand, personStand) ||
                other.personStand == personStand) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.enterpriseName, enterpriseName) ||
                other.enterpriseName == enterpriseName) &&
            (identical(other.areaId, areaId) || other.areaId == areaId) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchStep, personStand, age,
      creditCode, enterpriseName, areaId, channelType);

  @override
  String toString() {
    return 'QuizPreparationRequest(matchStep: $matchStep, personStand: $personStand, age: $age, creditCode: $creditCode, enterpriseName: $enterpriseName, areaId: $areaId, channelType: $channelType)';
  }
}

/// @nodoc
abstract mixin class _$QuizPreparationRequestCopyWith<$Res>
    implements $QuizPreparationRequestCopyWith<$Res> {
  factory _$QuizPreparationRequestCopyWith(_QuizPreparationRequest value,
          $Res Function(_QuizPreparationRequest) _then) =
      __$QuizPreparationRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int matchStep,
      String personStand,
      String age,
      String creditCode,
      String enterpriseName,
      String areaId,
      String channelType});
}

/// @nodoc
class __$QuizPreparationRequestCopyWithImpl<$Res>
    implements _$QuizPreparationRequestCopyWith<$Res> {
  __$QuizPreparationRequestCopyWithImpl(this._self, this._then);

  final _QuizPreparationRequest _self;
  final $Res Function(_QuizPreparationRequest) _then;

  /// Create a copy of QuizPreparationRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchStep = null,
    Object? personStand = null,
    Object? age = null,
    Object? creditCode = null,
    Object? enterpriseName = null,
    Object? areaId = null,
    Object? channelType = null,
  }) {
    return _then(_QuizPreparationRequest(
      matchStep: null == matchStep
          ? _self.matchStep
          : matchStep // ignore: cast_nullable_to_non_nullable
              as int,
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as String,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as String,
      creditCode: null == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String,
      enterpriseName: null == enterpriseName
          ? _self.enterpriseName
          : enterpriseName // ignore: cast_nullable_to_non_nullable
              as String,
      areaId: null == areaId
          ? _self.areaId
          : areaId // ignore: cast_nullable_to_non_nullable
              as String,
      channelType: null == channelType
          ? _self.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$QuizPreparationData {
  /// 企业ID
  String? get enterpriseId;

  /// 统一社会信用代码
  String? get creditCode;

  /// 匹配ID
  String get id;

  /// 匹配状态
  int get matchStatus;

  /// Create a copy of QuizPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizPreparationDataCopyWith<QuizPreparationData> get copyWith =>
      _$QuizPreparationDataCopyWithImpl<QuizPreparationData>(
          this as QuizPreparationData, _$identity);

  /// Serializes this QuizPreparationData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizPreparationData &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseId, creditCode, id, matchStatus);

  @override
  String toString() {
    return 'QuizPreparationData(enterpriseId: $enterpriseId, creditCode: $creditCode, id: $id, matchStatus: $matchStatus)';
  }
}

/// @nodoc
abstract mixin class $QuizPreparationDataCopyWith<$Res> {
  factory $QuizPreparationDataCopyWith(
          QuizPreparationData value, $Res Function(QuizPreparationData) _then) =
      _$QuizPreparationDataCopyWithImpl;
  @useResult
  $Res call(
      {String? enterpriseId, String? creditCode, String id, int matchStatus});
}

/// @nodoc
class _$QuizPreparationDataCopyWithImpl<$Res>
    implements $QuizPreparationDataCopyWith<$Res> {
  _$QuizPreparationDataCopyWithImpl(this._self, this._then);

  final QuizPreparationData _self;
  final $Res Function(QuizPreparationData) _then;

  /// Create a copy of QuizPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enterpriseId = freezed,
    Object? creditCode = freezed,
    Object? id = null,
    Object? matchStatus = null,
  }) {
    return _then(_self.copyWith(
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuizPreparationData implements QuizPreparationData {
  const _QuizPreparationData(
      {this.enterpriseId,
      this.creditCode,
      required this.id,
      required this.matchStatus});
  factory _QuizPreparationData.fromJson(Map<String, dynamic> json) =>
      _$QuizPreparationDataFromJson(json);

  /// 企业ID
  @override
  final String? enterpriseId;

  /// 统一社会信用代码
  @override
  final String? creditCode;

  /// 匹配ID
  @override
  final String id;

  /// 匹配状态
  @override
  final int matchStatus;

  /// Create a copy of QuizPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizPreparationDataCopyWith<_QuizPreparationData> get copyWith =>
      __$QuizPreparationDataCopyWithImpl<_QuizPreparationData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuizPreparationDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizPreparationData &&
            (identical(other.enterpriseId, enterpriseId) ||
                other.enterpriseId == enterpriseId) &&
            (identical(other.creditCode, creditCode) ||
                other.creditCode == creditCode) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, enterpriseId, creditCode, id, matchStatus);

  @override
  String toString() {
    return 'QuizPreparationData(enterpriseId: $enterpriseId, creditCode: $creditCode, id: $id, matchStatus: $matchStatus)';
  }
}

/// @nodoc
abstract mixin class _$QuizPreparationDataCopyWith<$Res>
    implements $QuizPreparationDataCopyWith<$Res> {
  factory _$QuizPreparationDataCopyWith(_QuizPreparationData value,
          $Res Function(_QuizPreparationData) _then) =
      __$QuizPreparationDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? enterpriseId, String? creditCode, String id, int matchStatus});
}

/// @nodoc
class __$QuizPreparationDataCopyWithImpl<$Res>
    implements _$QuizPreparationDataCopyWith<$Res> {
  __$QuizPreparationDataCopyWithImpl(this._self, this._then);

  final _QuizPreparationData _self;
  final $Res Function(_QuizPreparationData) _then;

  /// Create a copy of QuizPreparationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enterpriseId = freezed,
    Object? creditCode = freezed,
    Object? id = null,
    Object? matchStatus = null,
  }) {
    return _then(_QuizPreparationData(
      enterpriseId: freezed == enterpriseId
          ? _self.enterpriseId
          : enterpriseId // ignore: cast_nullable_to_non_nullable
              as String?,
      creditCode: freezed == creditCode
          ? _self.creditCode
          : creditCode // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _self.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
