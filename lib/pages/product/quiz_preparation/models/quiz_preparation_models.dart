import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/models/base_response.dart';

part 'quiz_preparation_models.freezed.dart';
part 'quiz_preparation_models.g.dart';

/// 答题准备页面参数
@freezed
abstract class QuizPreparationParams with _$QuizPreparationParams {
  const factory QuizPreparationParams({
    /// 统一社会信用代码
    required String creditCode,
    /// 企业名称
    required String enterpriseName,
    /// 申请人身份
    required String personStand,
    /// 年龄
    required String age,
    /// 地区ID
    required String areaId,
    /// 渠道类型
    required String channelType,
  }) = _QuizPreparationParams;

  factory QuizPreparationParams.fromJson(Map<String, dynamic> json) =>
      _$QuizPreparationParamsFromJson(json);
}

/// 答题准备请求参数
@freezed
abstract class QuizPreparationRequest with _$QuizPreparationRequest {
  const factory QuizPreparationRequest({
    /// 匹配步骤，默认为0
    @Default(0) int matchStep,
    /// 申请人身份 1
    required String personStand,
    /// 年龄
    required String age,
    /// 统一社会信用代码
    required String creditCode,
    /// 企业名称
    required String enterpriseName,
    /// 地区ID
    required String areaId,
    /// 渠道类型，默认为routine
    @Default("routine") String channelType,
  }) = _QuizPreparationRequest;

  factory QuizPreparationRequest.fromJson(Map<String, dynamic> json) =>
      _$QuizPreparationRequestFromJson(json);
}

/// 答题准备响应数据
@freezed
abstract class QuizPreparationData with _$QuizPreparationData {
  const factory QuizPreparationData({
    /// 企业ID
    String? enterpriseId,
    /// 统一社会信用代码
    String? creditCode,
    /// 匹配ID
    required String id,
    /// 匹配状态
    required int matchStatus,
  }) = _QuizPreparationData;

  factory QuizPreparationData.fromJson(Map<String, dynamic> json) =>
      _$QuizPreparationDataFromJson(json);
}

/// 答题准备响应
typedef QuizPreparationResponse = BaseResponse<QuizPreparationData>;

/// 申请人身份枚举
enum ApplicantType {
  legalPerson('1', '法人'),
  shareholder('2', '股东'),
  majorShareholder('3', '最大股东'),
  other('4', '其他');

  const ApplicantType(this.value, this.displayName);
  final String value;
  final String displayName;
}
