# 答题准备页面 (Quiz Preparation)

答题准备页面是答题功能的前置页面，用于显示企业信息并引导用户开始答题评估。

## 功能特性

- ✅ 显示企业名称和基本信息
- ✅ 精美的UI设计，包含插图和引导文字
- ✅ 同意条款复选框
- ✅ 调用答题匹配接口
- ✅ 自动跳转到答题页面
- ✅ 错误处理和加载状态
- ✅ 使用Riverpod进行状态管理
- ✅ 使用Freezed生成数据模型

## 目录结构

```
lib/pages/product/quiz_preparation/
├── models/
│   ├── quiz_preparation_models.dart      # 数据模型
│   ├── quiz_preparation_models.freezed.dart
│   └── quiz_preparation_models.g.dart
├── provider/
│   ├── quiz_preparation_provider.dart    # Riverpod状态管理
│   ├── quiz_preparation_provider.freezed.dart
│   └── quiz_preparation_provider.g.dart
├── services/
│   └── quiz_preparation_service.dart     # API服务层
├── quiz_preparation_page.dart            # 主页面
├── example_usage.dart                    # 使用示例
├── index.dart                           # 导出文件
└── README.md                            # 说明文档
```

## 使用方法

### 1. 基本使用

```dart
import 'package:zrreport/pages/product/quiz_preparation/index.dart';

// 创建页面参数
final params = QuizPreparationParams(
  creditCode: "91360500859876371Q",
  enterpriseName: "中国建设银行股份有限公司新余市分行",
  personStand: "1", // 
  age: "36",
  areaId: "20",
  channelType: "routine",
);

// 导航到答题准备页面
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => QuizPreparationPage(params: params),
  ),
);
```

### 2. 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| creditCode | String | ✅ | 统一社会信用代码 |
| enterpriseName | String | ✅ | 企业名称 |
| personStand | String | ✅ | 申请人身份
| age | String | ✅ | 申请人年龄 |
| areaId | String | ✅ | 地区ID |
| channelType | String | ❌ | 渠道类型，默认为"routine" |

### 3. API接口

页面会调用以下接口：

**POST** `/portal/cmsMatchEnterprise/create/questionMatch`

请求参数：
```json
{
  "matchStep": 0,
  "personStand": "1",
  "age": "36",
  "creditCode": "91360500859876371Q",
  "enterpriseName": "中国建设银行股份有限公司新余市分行",
  "areaId": "20",
  "channelType": "routine"
}
```

响应数据：
```json
{
  "traceId": "19a86c57ddd54a389ccde864990b2ed4",
  "code": 200,
  "message": "产品匹配中，请返回首页查看",
  "data": {
    "enterpriseId": null,
    "creditCode": "91360500859876371Q",
    "id": "=Q59jye_5dORLjCm-AK66cg===",
    "matchStatus": 16
  }
}
```

### 4. 页面流程

1. 用户进入页面，显示企业名称和引导信息
2. 用户勾选同意条款复选框
3. 点击"开始评估"按钮
4. 调用答题匹配接口
5. 成功后自动跳转到答题页面 (`QuizPage`)
6. 失败时显示错误信息

## 技术实现

### 状态管理

使用 Riverpod 进行状态管理，主要状态包括：
- `enterpriseName`: 企业名称
- `creditCode`: 统一社会信用代码
- `age`: 申请人年龄
- `areaId`: 地区ID
- `applicantType`: 申请人身份类型
- `isSubmitting`: 是否正在提交
- `submitError`: 提交错误信息

### 数据模型

使用 Freezed 生成不可变数据模型：
- `QuizPreparationParams`: 页面参数
- `QuizPreparationRequest`: API请求参数
- `QuizPreparationData`: API响应数据
- `QuizPreparationState`: 页面状态

### 错误处理

- 网络错误：显示"网络错误，请检查网络连接后重试"
- API错误：显示服务器返回的错误信息
- 参数验证：确保必填参数不为空

## 注意事项

1. 页面需要传入完整的参数才能正常工作
2. 确保网络连接正常，API接口可访问
3. 成功后会自动销毁当前页面并跳转到答题页面
4. 用户必须勾选同意条款才能开始评估
