import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'models/quiz_preparation_models.dart';
import 'quiz_preparation_page.dart';

/// 答题准备页面使用示例
class QuizPreparationExample {
  /// 导航到答题准备页面的示例方法
  static void navigateToQuizPreparation(BuildContext context) {
    // 创建页面参数
    final params = QuizPreparationParams(
      creditCode: "91360500859876371Q",
      enterpriseName: "中国建设银行股份有限公司新余市分行",
      personStand: "1", // 
      age: "36",
      areaId: "20",
      channelType: "routine",
    );

    // 导航到答题准备页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QuizPreparationPage(params: params),
      ),
    );
  }

  /// 从其他页面传递参数的示例
  static void navigateFromLoanMatching(
    BuildContext context, {
    required String creditCode,
    required String enterpriseName,
    required String personStand,
    required String age,
    required String areaId,
    String channelType = "routine",
  }) {
    final params = QuizPreparationParams(
      creditCode: creditCode,
      enterpriseName: enterpriseName,
      personStand: personStand,
      age: age,
      areaId: areaId,
      channelType: channelType,
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QuizPreparationPage(params: params),
      ),
    );
  }
}

/// 示例页面，展示如何调用答题准备页面
class ExamplePage extends ConsumerWidget {
  const ExamplePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('答题准备页面示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => QuizPreparationExample.navigateToQuizPreparation(context),
              child: const Text('打开答题准备页面'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => QuizPreparationExample.navigateFromLoanMatching(
                context,
                creditCode: "91360500859876371Q",
                enterpriseName: "中国建设银行股份有限公司新余市分行",
                personStand: "1",
                age: "36",
                areaId: "20",
              ),
              child: const Text('从贷款匹配页面跳转'),
            ),
          ],
        ),
      ),
    );
  }
}
