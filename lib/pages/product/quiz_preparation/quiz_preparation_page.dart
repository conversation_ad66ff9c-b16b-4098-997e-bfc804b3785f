import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zrreport/common/index.dart';
import 'models/quiz_preparation_models.dart';
import 'provider/quiz_preparation_provider.dart';

/// 答题准备页面
class QuizPreparationPage extends ConsumerStatefulWidget {
  /// 页面参数
  final QuizPreparationParams params;

  const QuizPreparationPage({
    super.key,
    required this.params,
  });

  @override
  ConsumerState<QuizPreparationPage> createState() => _QuizPreparationPageState();
}

class _QuizPreparationPageState extends ConsumerState<QuizPreparationPage> {
  bool _isAgreed = false;

  @override
  void initState() {
    super.initState();
    // 初始化页面数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notifier = ref.read(quizPreparationNotifierProvider.notifier);
      notifier.initializeWithParams(widget.params);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(quizPreparationNotifierProvider);
    final notifier = ref.read(quizPreparationNotifierProvider.notifier);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(title: Text(
          '答题匹配',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),),
      body: AnnotatedRegion(
        value: SystemUiOverlayStyle.dark,
        child: SafeArea(
          child: Column(
            children: [

              // 内容区域
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      const SizedBox(height: 24),

                      // 企业名称显示
                      _buildCompanyNameSection(),

                      const SizedBox(height: 40),

                      // 中间插图
                      _buildIllustration(),

                      const SizedBox(height: 40),

                      // 即将开始答题文字
                      _buildStartQuizSection(),

                      const SizedBox(height: 60),

                        // 开始评估按钮
                      _buildStartButton(state, notifier),

                      const SizedBox(height: 16),

                      // 同意条款复选框
                      _buildAgreementCheckbox(),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


  /// 构建企业名称显示区域
  Widget _buildCompanyNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Text(
              '公司全称',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          widget.params.enterpriseName,
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textColor8,
            fontWeight: FontWeight.w400,
          ),
        ),
        Divider(),
      ],
    );
  }

  /// 构建中间插图
  Widget _buildIllustration() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SvgPicture.asset(
        AssetsSvgs.matchQuizStartSvg,
        fit: BoxFit.contain,
      ),
    );
  }

  /// 构建即将开始答题区域
  Widget _buildStartQuizSection() {
    return Column(
      children: [
        const Text(
          '即将开始答题',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 12),
        const Text(
          '为保证结果精准，请尽可能提供准确信息',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// 构建开始评估按钮
  Widget _buildStartButton(QuizPreparationState state, QuizPreparationNotifier notifier) {
    return buildFilledButton(
      '开始评估',
      onPressed: (!_isAgreed || state.isSubmitting)
          ? null
          : () => notifier.startAssessment(context),
      backgroundColor:
          _isAgreed ? AppColors.primary : AppColors.disableBackground,
      fontColor: _isAgreed ? Colors.white : AppColors.textColor9,
    );
  }

  /// 构建同意条款复选框
  Widget _buildAgreementCheckbox() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreed = !_isAgreed;
            });
          },
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(
                color: _isAgreed ? AppColors.primary : const Color(0xFFCCCCCC),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(4),
              color: _isAgreed ? AppColors.primary : Colors.transparent,
            ),
            child: _isAgreed
                ? const Icon(
                    Icons.check,
                    size: 14,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 8),
        const Text(
          '我已阅读并同意',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
          ),
        ),
        GestureDetector(
          onTap: () {
          },
          child: const Text(
            '《授权协议》',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF1890FF),
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

}
