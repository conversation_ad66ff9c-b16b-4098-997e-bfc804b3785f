import 'dart:collection';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../api/quiz_api.dart';
import '../models/question.dart';
import '../models/answer_sheet.dart';

part 'quiz_provider.g.dart';
part 'quiz_provider.freezed.dart';

/// 答题状态
@freezed
abstract class QuizState with _$QuizState {
  const factory QuizState({
    /// 当前问题
    Question? currentQuestion,

    /// 选中的选项列表
    @Default([]) List<QuestionOption> selectedOptions,

    /// 是否加载中
    @Default(false) bool isLoading,

    /// 错误信息
    String? error,

    /// 是否提交中
    @Default(false) bool isSubmitting,
  }) = _QuizState;
}

/// 答题Provider
@riverpod
class Quiz extends _$Quiz {
  /// 匹配ID
  String? _matchId;

  /// 匹配类型
  String _matchType = '1';

  @override
  QuizState build() {
    return const QuizState();
  }

  Queue<Question> questionList = Queue();

  Map<String, List<QuestionOption>> options = {};

  /// 加载第一题
  Future<void> loadFirstQuestion() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await QuizApi.getFirstQuestion();
      if (response.data != null) {
        questionList.add(response.data!);
      }
      state = state.copyWith(
        currentQuestion: response.data,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// 根据ID加载问题
  Future<void> loadQuestionById(String questionId) async {
    try {
      final response = await QuizApi.getQuestionById(questionId);
      if (response.data != null) {
        questionList.add(response.data!);
        state = state.copyWith(
          currentQuestion: response.data,
          selectedOptions: options[response.data!.id] ?? [], // 恢复之前的选择
          isLoading: false,
        );
      } else {
        answerFinished();
      }
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// 选择选项（支持单选和多选）
  void selectOption(QuestionOption option) {
    final currentQuestion = state.currentQuestion;
    if (currentQuestion == null) return;

    final selectedOptions = List<QuestionOption>.from(state.selectedOptions);
    final isCurrentlySelected = selectedOptions.any((o) => o.id == option.id);

    // 判断是单选还是多选（type: 2=单选, 3=多选）
    if (currentQuestion.isSingleChoice) {
      // 单选逻辑
      if (isCurrentlySelected && currentQuestion.isOptional) {
        // 如果是选答题且当前选项已选中，则取消选择
        state = state.copyWith(selectedOptions: []);
        options[currentQuestion.id] = [];
      } else {
        // 否则替换选择
        state = state.copyWith(selectedOptions: [option]);
        options[currentQuestion.id] = [option];
      }
    } else if (currentQuestion.isMultipleChoice) {
      // 多选：切换选择状态
      if (isCurrentlySelected) {
        // 如果是必答题且只剩一个选项，不允许取消
        if (currentQuestion.isRequired && selectedOptions.length == 1) {
          return; // 不执行任何操作
        }
        selectedOptions.removeWhere((o) => o.id == option.id);
      } else {
        selectedOptions.add(option);
      }
      state = state.copyWith(selectedOptions: selectedOptions);
      options[currentQuestion.id] = selectedOptions;
    }
  }

  /// 检查选项是否被选中
  bool isOptionSelected(QuestionOption option) {
    return state.selectedOptions.any((o) => o.id == option.id);
  }

  /// 检查当前题目是否可以进入下一题
  bool get canGoToNext {
    final currentQuestion = state.currentQuestion;
    final selectedOptions = state.selectedOptions;

    if (currentQuestion == null) return false;

    // 如果是必答题，必须至少选择一个选项
    if (currentQuestion.isRequired) {
      return selectedOptions.isNotEmpty;
    }

    // 如果是选答题，可以不选择直接进入下一题
    return true;
  }

  void gotoPreviousQuestion() {
    if (questionList.length > 1) {
      final removed = questionList.removeLast();
      options.remove(removed.id);
      state = state.copyWith(
        currentQuestion: questionList.last,
        selectedOptions: options[questionList.last.id] ?? [], // 重置选择
        isLoading: false,
      );
    }
  }

  /// 进入下一题
  Future<void> goToNextQuestion() async {
    final selectedOptions = state.selectedOptions;
    if (selectedOptions.isNotEmpty) {
      // 对于单选，取第一个选项的下一题ID
      // 对于多选，可能需要根据业务逻辑决定如何处理多个选项的下一题
      final firstOption = selectedOptions.first;
      if (firstOption.nextQuestionId.isNotEmpty) {
        await loadQuestionById(firstOption.nextQuestionId);
      } else {
        // 没有下一题，答题完成
        answerFinished();
      }
    }
  }

  /// 是否有下一题
  bool get hasNextQuestion {
    final selectedOptions = state.selectedOptions;
    return selectedOptions.isNotEmpty &&
        selectedOptions.first.nextQuestionId.isNotEmpty;
  }

  /// 是否有下一题
  bool get hasPreviousQuestion {
    return questionList.length > 1;
  }

  /// 设置匹配信息
  void setMatchInfo(String matchId, String matchType) {
    _matchId = matchId;
    _matchType = matchType;
  }

  /// 答题完成回调
  void Function()? _onAnswerFinished;

  /// 设置答题完成回调
  void setAnswerFinishedCallback(void Function()? callback) {
    _onAnswerFinished = callback;
  }

  /// 答题完成
  void answerFinished() {
    _onAnswerFinished?.call();
  }

  /// 构建答题结果数据
  List<AnswerSheet> _buildAnswerSheetList() {
    final answerSheetList = <AnswerSheet>[];

    for (final entry in options.entries) {
      final questionId = entry.key;
      final selectedOptions = entry.value;

      if (selectedOptions.isEmpty) continue;

      // 找到对应的问题
      final question = questionList.firstWhere(
        (q) => q.id == questionId,
        orElse: () => questionList.first,
      );

      // 构建sheetVos
      final sheetVos = selectedOptions.map((option) {
        return SheetVo(
          optionsId: option.id,
          value: option.value,
        );
      }).toList();

      answerSheetList.add(AnswerSheet(
        conditions: question.conditions,
        questionType: question.type,
        questionId: questionId,
        sheetVos: sheetVos,
      ));
    }

    return answerSheetList;
  }

  /// 提交答题结果
  Future<void> submitAnswers() async {
    if (_matchId == null || _matchId!.isEmpty) {
      state = state.copyWith(error: '缺少匹配ID，无法提交答案');
      return;
    }

    state = state.copyWith(isSubmitting: true, error: null);

    try {
      final answerSheetList = _buildAnswerSheetList();

      final request = QuizSubmitRequest(
        matchId: _matchId!,
        matchType: _matchType,
        answerSheetList: answerSheetList,
      );

      await QuizApi.submitAnswerSheet(request);

      state = state.copyWith(isSubmitting: false);
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isSubmitting: false,
      );
    }
  }
}
