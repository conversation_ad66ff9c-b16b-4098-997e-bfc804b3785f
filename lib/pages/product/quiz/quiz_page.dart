import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'models/question.dart';
import 'provider/quiz_provider.dart';
import 'widgets/quiz_option_card.dart';
import 'widgets/submit_confirmation_dialog.dart';

/// 答题页面
class QuizPage extends ConsumerStatefulWidget {
  final String matchId;
  final String matchType;

  const QuizPage({
    super.key,
    required this.matchId,
    required this.matchType,
  });

  @override
  ConsumerState<QuizPage> createState() => _QuizPageState();
}

class _QuizPageState extends ConsumerState<QuizPage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时设置匹配信息并加载第一题
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final quizNotifier = ref.read(quizProvider.notifier);
      // 设置匹配信息
      quizNotifier.setMatchInfo(widget.matchId, widget.matchType);
      // 设置答题完成回调
      quizNotifier.setAnswerFinishedCallback(_showSubmitConfirmationDialog);
      // 加载第一题
      quizNotifier.loadFirstQuestion();
    });
  }

  @override
  Widget build(BuildContext context) {
    final quizState = ref.watch(quizProvider);
    final quizNotifier = ref.read(quizProvider.notifier);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text('开始答题'),
        backgroundColor: Colors.white,
        elevation: 0,
        
      ),
      body: quizState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : quizState.error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '加载失败',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.textColor6,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          quizNotifier.loadFirstQuestion();
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                )
              : quizState.currentQuestion != null
                  ? _buildQuizContent(quizState, quizNotifier)
                  : const SizedBox.shrink(),
    );
  }

  Widget _buildQuizContent(QuizState quizState, Quiz quizNotifier) {
    final question = quizState.currentQuestion!;
    
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Container(
              decoration: BoxDecoration(
                // color: AppColors.primary, // 添加背景色作为备用
                image: DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage(AssetsImages.quizHeaderPng),
                  fit: BoxFit.fitWidth,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _tyoeContainer(question),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 问题内容
                        _buildQuizTitle(question),
                        const SizedBox(height: 16),
                        // 提示文本
                        // if (question.isOptional)
                        //   Container(
                        //     padding: const EdgeInsets.all(12),
                        //     decoration: BoxDecoration(
                        //       color: Colors.orange.withValues(alpha: 0.1),
                        //       borderRadius: BorderRadius.circular(8),
                        //       border: Border.all(
                        //         color: Colors.orange.withValues(alpha: 0.3),
                        //         width: 1,
                        //       ),
                        //     ),
                        //     child: Row(
                        //       children: [
                        //         Icon(
                        //           Icons.info_outline,
                        //           color: Colors.orange[700],
                        //           size: 16,
                        //         ),
                        //         const SizedBox(width: 8),
                        //         Expanded(
                        //           child: Text(
                        //             '此题为选答题，可以不选择或再次点击已选项取消选择',
                        //             style: TextStyle(
                        //               fontSize: 12,
                        //               color: Colors.orange[700],
                        //               height: 1.3,
                        //             ),
                        //           ),
                        //         ),
                        //       ],
                        //     ),
                        //   ),
                        const SizedBox(height: 16),
                        // 选项列表
                        ...question.options.map((option) {
                          final isSelected =
                              quizNotifier.isOptionSelected(option);
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: QuizOptionCard(
                              option: option,
                              isSelected: isSelected,
                              isMultiChoice: question.isMultipleChoice,
                              onTap: () {
                                quizNotifier.selectOption(option);
                              },
                            ),
                          );
                        }),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // 底部按钮区域
        _bottomBar(quizState, quizNotifier),
      ],
    );
  }

  Container _bottomBar(QuizState quizState, Quiz quizNotifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFE5E5E5), width: 0.5),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 上一题按钮
            if (quizNotifier.hasPreviousQuestion)
              Expanded(
                child: GestureDetector(
                  onTap: quizNotifier.gotoPreviousQuestion,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: const Center(
                      child: Text(
                        '上一题',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              )
            else
              Expanded(child: const SizedBox()),
            const SizedBox(width: 16),
            // 下一题/提交按钮
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (quizNotifier.canGoToNext) {
                    if (quizNotifier.hasNextQuestion) {
                      quizNotifier.goToNextQuestion();
                    } else {
                      // 没有下一题，触发答题完成
                      quizNotifier.answerFinished();
                    }
                  }
                },
                child: Container(
                  height: 48,
                  decoration: BoxDecoration(
                    color: quizNotifier.canGoToNext
                        ? AppColors.primary
                        : AppColors.textColor9,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Center(
                    child: quizState.isSubmitting
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            '下一题',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Text _buildQuizTitle(Question question) {
    return Text(
      question.label,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textColor1,
        height: 1.4,
      ),
    );
  }

  _tyoeContainer(Question question) {
    return Container(
      width: double.infinity,
      height: 45,

      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // 题目类型标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              question.typeDisplayText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 必答/选答标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: question.isRequired
                  ? Colors.red.withValues(alpha: 0.8)
                  : Colors.orange.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              question.requiredDisplayText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }



  /// 显示提交确认对话框
  void _showSubmitConfirmationDialog() {
    SubmitConfirmationDialog.show(
      context,
      onSubmit: () async {
        final quizNotifier = ref.read(quizProvider.notifier);
        await quizNotifier.submitAnswers();

        // 提交成功后关闭页面
        if (mounted) {
          Navigator.of(context).pop();
        }
      },
    );
  }
}
