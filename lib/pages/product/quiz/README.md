# 答题模块

这是一个基于Flutter和Riverpod的答题页面模块，支持单选题答题功能。

## 功能特性

- ✅ 单选题答题
- ✅ 多选题答题
- ✅ 动态加载问题
- ✅ 选项选择状态管理
- ✅ 下一题导航
- ✅ 上一题回退
- ✅ 响应式UI设计
- ✅ 错误处理和重试机制
- ✅ 自动识别题目类型（单选/多选）
- ✅ 必答题和选答题支持
- ✅ 答题结果提交
- ✅ 答题完成页面
- ✅ 答题完成确认对话框

## 文件结构

```
lib/pages/product/quiz/
├── api/
│   └── quiz_api.dart           # API服务层
├── models/
│   ├── question.dart           # 问题数据模型
│   ├── question.freezed.dart   # Freezed生成文件
│   └── question.g.dart         # JSON序列化生成文件
├── provider/
│   ├── quiz_provider.dart      # Riverpod状态管理
│   ├── quiz_provider.freezed.dart
│   └── quiz_provider.g.dart
├── widgets/
│   └── quiz_option_card.dart   # 选项卡片组件
├── quiz_page.dart              # 主答题页面
├── test_quiz_page.dart         # 测试页面
├── index.dart                  # 导出文件
└── README.md                   # 说明文档
```

## 使用方法

### 1. 导航到答题页面

```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const QuizPage(
      matchId: "your_match_id",
      matchType: "1", // 匹配类型
    ),
  ),
);
```

**参数说明：**
- `matchId`: 匹配ID，用于标识本次答题会话
- `matchType`: 匹配类型，通常为"1"

### 2. 答题完成流程

当用户完成最后一题时，会自动弹出提交确认对话框：

1. **触发时机**: 当没有下一题时，点击"下一题"按钮会触发`answerFinished()`
2. **确认对话框**: 显示"最后一道题答完啦，点击提交上传吧~"的提示
3. **用户确认**: 用户点击"提交"按钮后正式提交答案
4. **提交完成**: 提交成功后自动关闭答题页面

### 3. 自定义API端点

如需修改API端点，请编辑 `api/quiz_api.dart` 文件：

```dart
static Future<BaseResponse<Question>> getFirstQuestion() async {
  final response = await SXHttpService.to.get('/question/getFirstQuestion');
  // ...
}
```

## API接口

### 获取第一题
- **URL**: `/question/getFirstQuestion`
- **方法**: GET
- **返回**: Question对象

### 根据ID获取问题
- **URL**: `/question/{questionId}`
- **方法**: GET
- **参数**: questionId (路径参数)
- **返回**: Question对象

## 数据模型

### Question (问题)
- `id`: 问题ID
- `label`: 问题标题
- `type`: 问题类型（2=单选，3=多选）
- `options`: 选项列表
- 其他字段...

#### Question扩展方法
- `isSingleChoice`: 是否为单选题（type == 2）
- `isMultipleChoice`: 是否为多选题（type == 3）
- `typeDisplayText`: 获取题目类型显示文本（"单选"或"多选"）

### QuestionOption (选项)
- `id`: 选项ID
- `label`: 选项文本
- `nextQuestionId`: 下一题ID
- `selectStatus`: 选择状态

## 状态管理

使用Riverpod进行状态管理，主要状态包括：
- `currentQuestion`: 当前问题
- `selectedOptions`: 选中的选项列表（支持单选和多选）
- `isLoading`: 加载状态
- `error`: 错误信息

### 主要方法
- `selectOption(option)`: 选择选项（自动处理单选/多选逻辑）
- `isOptionSelected(option)`: 检查选项是否被选中
- `goToNextQuestion()`: 进入下一题
- `gotoPreviousQuestion()`: 返回上一题
- `hasNextQuestion`: 是否有下一题
- `hasPreviousQuestion`: 是否有上一题

## 样式定制

页面使用了项目中的AppColors颜色系统：
- `AppColors.primary`: 主色调
- `AppColors.textColor1/3/6/8/9`: 文本颜色
- 其他颜色定义在 `lib/common/style/colors.dart`

## 注意事项

1. 确保已正确配置网络请求服务
2. 需要运行 `dart run build_runner build` 生成相关文件
3. 页面依赖项目的通用组件和样式系统
