// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'question.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuestionOption {
  /// 选项ID
  String get id;

  /// 问题ID
  String get questionId;

  /// 下一题ID
  String get nextQuestionId;

  /// 下一题标签
  String? get nextQuestionLabel;

  /// 选项标签
  String get label;

  /// 选项值
  String get value;

  /// 选择状态
  bool get selectStatus;

  /// Create a copy of QuestionOption
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuestionOptionCopyWith<QuestionOption> get copyWith =>
      _$QuestionOptionCopyWithImpl<QuestionOption>(
          this as QuestionOption, _$identity);

  /// Serializes this QuestionOption to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuestionOption &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.questionId, questionId) ||
                other.questionId == questionId) &&
            (identical(other.nextQuestionId, nextQuestionId) ||
                other.nextQuestionId == nextQuestionId) &&
            (identical(other.nextQuestionLabel, nextQuestionLabel) ||
                other.nextQuestionLabel == nextQuestionLabel) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.selectStatus, selectStatus) ||
                other.selectStatus == selectStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, questionId, nextQuestionId,
      nextQuestionLabel, label, value, selectStatus);

  @override
  String toString() {
    return 'QuestionOption(id: $id, questionId: $questionId, nextQuestionId: $nextQuestionId, nextQuestionLabel: $nextQuestionLabel, label: $label, value: $value, selectStatus: $selectStatus)';
  }
}

/// @nodoc
abstract mixin class $QuestionOptionCopyWith<$Res> {
  factory $QuestionOptionCopyWith(
          QuestionOption value, $Res Function(QuestionOption) _then) =
      _$QuestionOptionCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String questionId,
      String nextQuestionId,
      String? nextQuestionLabel,
      String label,
      String value,
      bool selectStatus});
}

/// @nodoc
class _$QuestionOptionCopyWithImpl<$Res>
    implements $QuestionOptionCopyWith<$Res> {
  _$QuestionOptionCopyWithImpl(this._self, this._then);

  final QuestionOption _self;
  final $Res Function(QuestionOption) _then;

  /// Create a copy of QuestionOption
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? questionId = null,
    Object? nextQuestionId = null,
    Object? nextQuestionLabel = freezed,
    Object? label = null,
    Object? value = null,
    Object? selectStatus = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      questionId: null == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String,
      nextQuestionId: null == nextQuestionId
          ? _self.nextQuestionId
          : nextQuestionId // ignore: cast_nullable_to_non_nullable
              as String,
      nextQuestionLabel: freezed == nextQuestionLabel
          ? _self.nextQuestionLabel
          : nextQuestionLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      selectStatus: null == selectStatus
          ? _self.selectStatus
          : selectStatus // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuestionOption implements QuestionOption {
  const _QuestionOption(
      {this.id = '',
      this.questionId = '',
      this.nextQuestionId = '',
      this.nextQuestionLabel,
      this.label = '',
      this.value = '',
      this.selectStatus = false});
  factory _QuestionOption.fromJson(Map<String, dynamic> json) =>
      _$QuestionOptionFromJson(json);

  /// 选项ID
  @override
  @JsonKey()
  final String id;

  /// 问题ID
  @override
  @JsonKey()
  final String questionId;

  /// 下一题ID
  @override
  @JsonKey()
  final String nextQuestionId;

  /// 下一题标签
  @override
  final String? nextQuestionLabel;

  /// 选项标签
  @override
  @JsonKey()
  final String label;

  /// 选项值
  @override
  @JsonKey()
  final String value;

  /// 选择状态
  @override
  @JsonKey()
  final bool selectStatus;

  /// Create a copy of QuestionOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuestionOptionCopyWith<_QuestionOption> get copyWith =>
      __$QuestionOptionCopyWithImpl<_QuestionOption>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuestionOptionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuestionOption &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.questionId, questionId) ||
                other.questionId == questionId) &&
            (identical(other.nextQuestionId, nextQuestionId) ||
                other.nextQuestionId == nextQuestionId) &&
            (identical(other.nextQuestionLabel, nextQuestionLabel) ||
                other.nextQuestionLabel == nextQuestionLabel) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.selectStatus, selectStatus) ||
                other.selectStatus == selectStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, questionId, nextQuestionId,
      nextQuestionLabel, label, value, selectStatus);

  @override
  String toString() {
    return 'QuestionOption(id: $id, questionId: $questionId, nextQuestionId: $nextQuestionId, nextQuestionLabel: $nextQuestionLabel, label: $label, value: $value, selectStatus: $selectStatus)';
  }
}

/// @nodoc
abstract mixin class _$QuestionOptionCopyWith<$Res>
    implements $QuestionOptionCopyWith<$Res> {
  factory _$QuestionOptionCopyWith(
          _QuestionOption value, $Res Function(_QuestionOption) _then) =
      __$QuestionOptionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String questionId,
      String nextQuestionId,
      String? nextQuestionLabel,
      String label,
      String value,
      bool selectStatus});
}

/// @nodoc
class __$QuestionOptionCopyWithImpl<$Res>
    implements _$QuestionOptionCopyWith<$Res> {
  __$QuestionOptionCopyWithImpl(this._self, this._then);

  final _QuestionOption _self;
  final $Res Function(_QuestionOption) _then;

  /// Create a copy of QuestionOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? questionId = null,
    Object? nextQuestionId = null,
    Object? nextQuestionLabel = freezed,
    Object? label = null,
    Object? value = null,
    Object? selectStatus = null,
  }) {
    return _then(_QuestionOption(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      questionId: null == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String,
      nextQuestionId: null == nextQuestionId
          ? _self.nextQuestionId
          : nextQuestionId // ignore: cast_nullable_to_non_nullable
              as String,
      nextQuestionLabel: freezed == nextQuestionLabel
          ? _self.nextQuestionLabel
          : nextQuestionLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      selectStatus: null == selectStatus
          ? _self.selectStatus
          : selectStatus // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$Question {
  /// 问题ID
  String get id;

  /// 分类ID
  String get categoryId;

  /// 匹配类型
  String get matchType;

  /// 条件
  String get conditions;

  /// 条件名称
  String? get conditionsName;

  /// 元数据ID
  String get metaId;

  /// 发布状态
  int get publishStatus;

  /// 问题标签
  String get label;

  /// 问题类型 2->单选，3->多选
  int get type;

  /// 是否必须,0->否，1->是
  int get must;

  /// 备注
  String get note;

  /// 创建者
  String get createBy;

  /// 创建时间
  String get createTime;

  /// 选项列表
  List<QuestionOption> get options;

  /// 答案列表
  List<dynamic> get answers;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuestionCopyWith<Question> get copyWith =>
      _$QuestionCopyWithImpl<Question>(this as Question, _$identity);

  /// Serializes this Question to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Question &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            (identical(other.conditions, conditions) ||
                other.conditions == conditions) &&
            (identical(other.conditionsName, conditionsName) ||
                other.conditionsName == conditionsName) &&
            (identical(other.metaId, metaId) || other.metaId == metaId) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.must, must) || other.must == must) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            const DeepCollectionEquality().equals(other.options, options) &&
            const DeepCollectionEquality().equals(other.answers, answers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      categoryId,
      matchType,
      conditions,
      conditionsName,
      metaId,
      publishStatus,
      label,
      type,
      must,
      note,
      createBy,
      createTime,
      const DeepCollectionEquality().hash(options),
      const DeepCollectionEquality().hash(answers));

  @override
  String toString() {
    return 'Question(id: $id, categoryId: $categoryId, matchType: $matchType, conditions: $conditions, conditionsName: $conditionsName, metaId: $metaId, publishStatus: $publishStatus, label: $label, type: $type, must: $must, note: $note, createBy: $createBy, createTime: $createTime, options: $options, answers: $answers)';
  }
}

/// @nodoc
abstract mixin class $QuestionCopyWith<$Res> {
  factory $QuestionCopyWith(Question value, $Res Function(Question) _then) =
      _$QuestionCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String categoryId,
      String matchType,
      String conditions,
      String? conditionsName,
      String metaId,
      int publishStatus,
      String label,
      int type,
      int must,
      String note,
      String createBy,
      String createTime,
      List<QuestionOption> options,
      List<dynamic> answers});
}

/// @nodoc
class _$QuestionCopyWithImpl<$Res> implements $QuestionCopyWith<$Res> {
  _$QuestionCopyWithImpl(this._self, this._then);

  final Question _self;
  final $Res Function(Question) _then;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? categoryId = null,
    Object? matchType = null,
    Object? conditions = null,
    Object? conditionsName = freezed,
    Object? metaId = null,
    Object? publishStatus = null,
    Object? label = null,
    Object? type = null,
    Object? must = null,
    Object? note = null,
    Object? createBy = null,
    Object? createTime = null,
    Object? options = null,
    Object? answers = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      matchType: null == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as String,
      conditions: null == conditions
          ? _self.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as String,
      conditionsName: freezed == conditionsName
          ? _self.conditionsName
          : conditionsName // ignore: cast_nullable_to_non_nullable
              as String?,
      metaId: null == metaId
          ? _self.metaId
          : metaId // ignore: cast_nullable_to_non_nullable
              as String,
      publishStatus: null == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as int,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      must: null == must
          ? _self.must
          : must // ignore: cast_nullable_to_non_nullable
              as int,
      note: null == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String,
      createBy: null == createBy
          ? _self.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as String,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      options: null == options
          ? _self.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<QuestionOption>,
      answers: null == answers
          ? _self.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Question implements Question {
  const _Question(
      {this.id = '',
      this.categoryId = '',
      this.matchType = '',
      this.conditions = '',
      this.conditionsName,
      this.metaId = '',
      this.publishStatus = 0,
      this.label = '',
      this.type = 0,
      this.must = 0,
      this.note = '',
      this.createBy = '',
      this.createTime = '',
      final List<QuestionOption> options = const [],
      final List<dynamic> answers = const []})
      : _options = options,
        _answers = answers;
  factory _Question.fromJson(Map<String, dynamic> json) =>
      _$QuestionFromJson(json);

  /// 问题ID
  @override
  @JsonKey()
  final String id;

  /// 分类ID
  @override
  @JsonKey()
  final String categoryId;

  /// 匹配类型
  @override
  @JsonKey()
  final String matchType;

  /// 条件
  @override
  @JsonKey()
  final String conditions;

  /// 条件名称
  @override
  final String? conditionsName;

  /// 元数据ID
  @override
  @JsonKey()
  final String metaId;

  /// 发布状态
  @override
  @JsonKey()
  final int publishStatus;

  /// 问题标签
  @override
  @JsonKey()
  final String label;

  /// 问题类型 2->单选，3->多选
  @override
  @JsonKey()
  final int type;

  /// 是否必须,0->否，1->是
  @override
  @JsonKey()
  final int must;

  /// 备注
  @override
  @JsonKey()
  final String note;

  /// 创建者
  @override
  @JsonKey()
  final String createBy;

  /// 创建时间
  @override
  @JsonKey()
  final String createTime;

  /// 选项列表
  final List<QuestionOption> _options;

  /// 选项列表
  @override
  @JsonKey()
  List<QuestionOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  /// 答案列表
  final List<dynamic> _answers;

  /// 答案列表
  @override
  @JsonKey()
  List<dynamic> get answers {
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answers);
  }

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuestionCopyWith<_Question> get copyWith =>
      __$QuestionCopyWithImpl<_Question>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuestionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Question &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            (identical(other.conditions, conditions) ||
                other.conditions == conditions) &&
            (identical(other.conditionsName, conditionsName) ||
                other.conditionsName == conditionsName) &&
            (identical(other.metaId, metaId) || other.metaId == metaId) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.must, must) || other.must == must) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            const DeepCollectionEquality().equals(other._options, _options) &&
            const DeepCollectionEquality().equals(other._answers, _answers));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      categoryId,
      matchType,
      conditions,
      conditionsName,
      metaId,
      publishStatus,
      label,
      type,
      must,
      note,
      createBy,
      createTime,
      const DeepCollectionEquality().hash(_options),
      const DeepCollectionEquality().hash(_answers));

  @override
  String toString() {
    return 'Question(id: $id, categoryId: $categoryId, matchType: $matchType, conditions: $conditions, conditionsName: $conditionsName, metaId: $metaId, publishStatus: $publishStatus, label: $label, type: $type, must: $must, note: $note, createBy: $createBy, createTime: $createTime, options: $options, answers: $answers)';
  }
}

/// @nodoc
abstract mixin class _$QuestionCopyWith<$Res>
    implements $QuestionCopyWith<$Res> {
  factory _$QuestionCopyWith(_Question value, $Res Function(_Question) _then) =
      __$QuestionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String categoryId,
      String matchType,
      String conditions,
      String? conditionsName,
      String metaId,
      int publishStatus,
      String label,
      int type,
      int must,
      String note,
      String createBy,
      String createTime,
      List<QuestionOption> options,
      List<dynamic> answers});
}

/// @nodoc
class __$QuestionCopyWithImpl<$Res> implements _$QuestionCopyWith<$Res> {
  __$QuestionCopyWithImpl(this._self, this._then);

  final _Question _self;
  final $Res Function(_Question) _then;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? categoryId = null,
    Object? matchType = null,
    Object? conditions = null,
    Object? conditionsName = freezed,
    Object? metaId = null,
    Object? publishStatus = null,
    Object? label = null,
    Object? type = null,
    Object? must = null,
    Object? note = null,
    Object? createBy = null,
    Object? createTime = null,
    Object? options = null,
    Object? answers = null,
  }) {
    return _then(_Question(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      categoryId: null == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      matchType: null == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as String,
      conditions: null == conditions
          ? _self.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as String,
      conditionsName: freezed == conditionsName
          ? _self.conditionsName
          : conditionsName // ignore: cast_nullable_to_non_nullable
              as String?,
      metaId: null == metaId
          ? _self.metaId
          : metaId // ignore: cast_nullable_to_non_nullable
              as String,
      publishStatus: null == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as int,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      must: null == must
          ? _self.must
          : must // ignore: cast_nullable_to_non_nullable
              as int,
      note: null == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String,
      createBy: null == createBy
          ? _self.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as String,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      options: null == options
          ? _self._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<QuestionOption>,
      answers: null == answers
          ? _self._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
    ));
  }
}

// dart format on
