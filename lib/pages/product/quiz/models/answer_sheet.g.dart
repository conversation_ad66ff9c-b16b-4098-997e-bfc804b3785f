// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'answer_sheet.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SheetVo _$SheetVoFromJson(Map<String, dynamic> json) => _SheetVo(
      optionsId: json['optionsId'] as String? ?? '',
      value: json['value'] as String? ?? '',
    );

Map<String, dynamic> _$SheetVoToJson(_SheetVo instance) => <String, dynamic>{
      'optionsId': instance.optionsId,
      'value': instance.value,
    };

_AnswerSheet _$AnswerSheetFromJson(Map<String, dynamic> json) => _AnswerSheet(
      conditions: json['conditions'] as String? ?? '',
      questionType: (json['questionType'] as num?)?.toInt() ?? 0,
      questionId: json['questionId'] as String? ?? '',
      sheetVos: (json['sheetVos'] as List<dynamic>?)
              ?.map((e) => SheetVo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$AnswerSheetToJson(_AnswerSheet instance) =>
    <String, dynamic>{
      'conditions': instance.conditions,
      'questionType': instance.questionType,
      'questionId': instance.questionId,
      'sheetVos': instance.sheetVos,
    };

_QuizSubmitRequest _$QuizSubmitRequestFromJson(Map<String, dynamic> json) =>
    _QuizSubmitRequest(
      matchId: json['matchId'] as String? ?? '',
      matchType: json['matchType'] as String? ?? '',
      answerSheetList: (json['answerSheetList'] as List<dynamic>?)
              ?.map((e) => AnswerSheet.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$QuizSubmitRequestToJson(_QuizSubmitRequest instance) =>
    <String, dynamic>{
      'matchId': instance.matchId,
      'matchType': instance.matchType,
      'answerSheetList': instance.answerSheetList,
    };
