import 'package:freezed_annotation/freezed_annotation.dart';

part 'answer_sheet.freezed.dart';
part 'answer_sheet.g.dart';

/// 答案选项模型
@freezed
abstract class SheetVo with _$SheetVo {
  const factory SheetVo({
    /// 选项ID
    @Default('') String optionsId,

    /// 选项值
    @Default('') String value,
  }) = _SheetVo;

  factory SheetVo.fromJson(Map<String, dynamic> json) =>
      _$SheetVoFromJson(json);
}

/// 答题表单模型
@freezed
abstract class AnswerSheet with _$AnswerSheet {
  const factory AnswerSheet({
    /// 条件
    @Default('') String conditions,

    /// 问题类型
    @Default(0) int questionType,

    /// 问题ID
    @Default('') String questionId,

    /// 答案选项列表
    @Default([]) List<SheetVo> sheetVos,
  }) = _AnswerSheet;

  factory AnswerSheet.fromJson(Map<String, dynamic> json) =>
      _$AnswerSheetFromJson(json);
}

/// 答题提交请求模型
@freezed
abstract class QuizSubmitRequest with _$QuizSubmitRequest {
  const factory QuizSubmitRequest({
    /// 匹配ID
    @Default('') String matchId,

    /// 匹配类型
    @Default('') String matchType,

    /// 答题表单列表
    @Default([]) List<AnswerSheet> answerSheetList,
  }) = _QuizSubmitRequest;

  factory QuizSubmitRequest.fromJson(Map<String, dynamic> json) =>
      _$QuizSubmitRequestFromJson(json);
}
