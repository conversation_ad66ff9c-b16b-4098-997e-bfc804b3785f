import 'package:flutter_test/flutter_test.dart';
import '../models/question.dart';

void main() {
  group('Question Type Tests', () {
    test('单选题类型判断', () {
      final singleChoiceQuestion = Question(
        id: '1',
        type: 2, // 单选
        label: '这是一个单选题',
      );

      expect(singleChoiceQuestion.isSingleChoice, true);
      expect(singleChoiceQuestion.isMultipleChoice, false);
      expect(singleChoiceQuestion.typeDisplayText, '单选');
    });

    test('多选题类型判断', () {
      final multipleChoiceQuestion = Question(
        id: '2',
        type: 3, // 多选
        label: '这是一个多选题',
      );

      expect(multipleChoiceQuestion.isSingleChoice, false);
      expect(multipleChoiceQuestion.isMultipleChoice, true);
      expect(multipleChoiceQuestion.typeDisplayText, '多选');
    });

    test('未知类型题目', () {
      final unknownTypeQuestion = Question(
        id: '3',
        type: 1, // 未知类型
        label: '这是一个未知类型题目',
      );

      expect(unknownTypeQuestion.isSingleChoice, false);
      expect(unknownTypeQuestion.isMultipleChoice, false);
      expect(unknownTypeQuestion.typeDisplayText, '多选'); // 默认显示多选
    });
  });

  group('QuestionOption Tests', () {
    test('选项基本属性', () {
      final option = QuestionOption(
        id: 'opt1',
        questionId: 'q1',
        label: '选项A',
        nextQuestionId: 'q2',
      );

      expect(option.id, 'opt1');
      expect(option.questionId, 'q1');
      expect(option.label, '选项A');
      expect(option.nextQuestionId, 'q2');
      expect(option.selectStatus, false);
    });
  });
}
