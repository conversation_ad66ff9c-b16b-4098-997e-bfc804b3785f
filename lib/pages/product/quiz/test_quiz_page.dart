import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'quiz_page.dart';

/// 测试答题页面的入口
class TestQuizPage extends ConsumerWidget {
  const TestQuizPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('答题测试'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const QuizPage(
                  matchId: "test_match_id",
                  matchType: "1",
                ),
              ),
            );
          },
          child: const Text('开始答题'),
        ),
      ),
    );
  }
}
