import 'package:zrreport/common/index.dart';
import '../models/question.dart';
import '../models/answer_sheet.dart';

/// 答题API服务
class QuizApi {
  /// 获取第一题
  static Future<BaseResponse<Question>> getFirstQuestion() async {
    final response = await SXHttpService.to.get('/question/getFirstQuestion');
    return BaseResponse.fromJson(
      response.data,
      (data) => Question.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 根据问题ID获取问题
  static Future<BaseResponse<Question>> getQuestionById(String questionId) async {
    final response = await SXHttpService.to.get('/question/$questionId');
    return BaseResponse.fromJson(
      response.data,
      (data) => Question.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 提交答题结果
  static Future<BaseResponse> submitAnswerSheet(
      QuizSubmitRequest request) async {
    final response = await SXHttpService.to.post(
      '/question/createAnswerSheet',
      data: request.toJson(),
    );
    return BaseResponse.fromJson(response.data, (data) => data);
  }
}
