import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/loan_detail_models.dart';

part 'loan_detail_state.freezed.dart';

/// 贷款商品详情页面状态
@freezed
abstract class LoanDetailState with _$LoanDetailState {
  const factory LoanDetailState({
    /// 是否正在加载
    @Default(false) bool isLoading,
    
    /// 贷款商品详情数据
    LoanDetailModel? loanDetail,
    
    /// 错误信息
    String? error,
    
    /// 导航栏是否展开
    @Default(true) bool isNavigationExpanded,
    
    /// 当前选中的导航项
    @Default('个人要求') String selectedNavigation,
    
    /// 导航项列表
    @Default([
      NavigationItem(title: '个人要求', key: 'personal', isSelected: true),
      NavigationItem(title: '企业要求', key: 'company'),
      NavigationItem(title: '征信要求', key: 'credit'),
      NavigationItem(title: '准入地区', key: 'area'),
      NavigationItem(title: '禁入行业', key: 'industry'),
    ])
    List<NavigationItem> navigationItems,
  }) = _LoanDetailState;
}

/// Section项目模型
class SectionItem {
  const SectionItem({
    required this.key,
    required this.title,
    required this.content,
    this.type = SectionType.text,
  });

  final String key;
  final String title;
  final String content;
  final SectionType type;
}

/// Section类型枚举
enum SectionType {
  text, // 普通文本内容
  custom, // 自定义要求
}

/// 扩展方法
extension LoanDetailStateX on LoanDetailState {
  /// 获取优势数据列表
  List<String> get advantageList {
    if (loanDetail?.advantageDataJson == null) return [];
    try {
      // 解析JSON字符串，例如: ["快速放款","优质服务","简单申请"]
      final String jsonStr = loanDetail!.advantageDataJson!;
      // 移除方括号并分割字符串
      final String cleanStr = jsonStr.replaceAll('[', '').replaceAll(']', '').replaceAll('"', '');
      return cleanStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
    } catch (e) {
      return [];
    }
  }
  
  /// 获取还款方式列表
  List<String> get repaymentTypeList {
    if (loanDetail?.repaymentType == null) return [];
    final List<String> types = loanDetail!.repaymentType.split(',');
    return types.map((type) {
      switch (type.trim()) {
        case '1':
          return '等额本息';
        case '7':
          return '先息后本';
        case '10':
          return '随借随还';
        default:
          return '其他';
      }
    }).toList();
  }
  
  /// 获取贷款期限列表
  List<String> get loanermList {
    if (loanDetail?.loanTerm == null) return [];
    return loanDetail!.loanTerm.split(',').map((e) => '${e.trim()}个月').toList();
  }

  /// 获取个人要求文
  String get personalRequirmentsText {
    final List<String> requirements = [];

    if (loanDetail?.corporateAge != null && loanDetail?.corporateMaxAge != null) {
      requirements.add('申请人年龄 ${loanDetail!.corporateAge}-${loanDetail!.corporateMaxAge}周岁');
    }

    // 添加法人申请要求
    if (loanDetail?.legalApplyRequire != null) {
      final lines = loanDetail!.legalApplyRequire!.split('\n');
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isNotEmpty) {
          requirements.add(trimmedLine);
        }
      }
    }

    return requirements.join('\n');
  }
  
  /// 获取企业要求文本
  String get companyRequirementsText {
    if (loanDetail?.companyApplyRequire != null) {
      return loanDetail!.companyApplyRequire!;
    }
    return '';
  }

  /// 获取征信要求文本
  String get creditRequirementsText {
    if (loanDetail?.creditRequire != null) {
      return loanDetail!.creditRequire!;
    }
    return '';
  }

  /// 获取准入地区文本
  String get accessAreaText {
    if (loanDetail?.accessIndustry != null) {
      return loanDetail!.accessIndustry!;
    }
    return '';
  }

  /// 获取禁入行业文本
  String get prohibitIndustryText {
    if (loanDetail?.prohibitIndustry != null) {
      return loanDetail!.prohibitIndustry!;
    }
    return '';
  }

  /// 获取所有section列表（用于ScrollablePositionedList）
  List<SectionItem> get sectionItems {
    if (loanDetail == null) return [];

    // 构建个人要求内容
    final personalRequirements = <String>[];
    if (loanDetail!.corporateAge != null &&
        loanDetail!.corporateMaxAge != null) {
      personalRequirements.add(
          '申请人年龄 ${loanDetail!.corporateAge}-${loanDetail!.corporateMaxAge}周岁');
    }
    if (loanDetail!.legalApplyRequire != null) {
      final lines = loanDetail!.legalApplyRequire!.split('\n');
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isNotEmpty) {
          personalRequirements.add(trimmedLine);
        }
      }
    }

    final items = <SectionItem>[
      SectionItem(
        key: 'personal',
        title: '个人要求',
        content: personalRequirements.join('\n'),
      ),
      SectionItem(
        key: 'company',
        title: '企业要求',
        content: loanDetail!.companyApplyRequire ?? '',
      ),
      SectionItem(
        key: 'credit',
        title: '征信要求',
        content: loanDetail!.creditRequire ?? '',
      ),
      SectionItem(
        key: 'area',
        title: '准入地区',
        content: loanDetail!.accessIndustry ?? '',
      ),
      SectionItem(
        key: 'industry',
        title: '禁入行业',
        content: loanDetail!.prohibitIndustry ?? '',
      ),
    ];

    // 添加自定义要求
    if (loanDetail!.customRequireList.isNotEmpty) {
      for (int i = 0; i < loanDetail!.customRequireList.length; i++) {
        final customRequire = loanDetail!.customRequireList[i];
        items.add(SectionItem(
          key: 'custom_$i',
          title: customRequire.title,
          content: customRequire.content,
          type: SectionType.custom,
        ));
      }
    }

    return items;
  }
}
