import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'provider/loan_detail_provider.dart';
import 'provider/loan_detail_state.dart';
import 'widgets/loan_detail_header.dart';
import 'widgets/loan_detail_navigation.dart';
import 'widgets/loan_detail_scrollable_content.dart';

/// 贷款商品详情页面
class LoanDetailPage extends ConsumerStatefulWidget {
  /// 商品ID
  final String productId;

  const LoanDetailPage({
    super.key,
    required this.productId,
  });

  @override
  ConsumerState<LoanDetailPage> createState() => _LoanDetailPageState();
}

class _LoanDetailPageState extends ConsumerState<LoanDetailPage> {
  @override
  void initState() {
    super.initState();

    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(loanDetailNotifierProvider.notifier).initialize(widget.productId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(loanDetailNotifierProvider);
    final notifier = ref.read(loanDetailNotifierProvider.notifier);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.textColor1),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          state.loanDetail?.name ?? '贷款详情',
          style: const TextStyle(
            color: AppColors.textColor1,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: _buildBody(state, notifier),
    );
  }

  Widget _buildBody(LoanDetailState state, LoanDetailNotifier notifier) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorStatusWidget(
        text: state.error!,
        onAttempt: () => notifier.reload(widget.productId),
      );
    }

    if (state.loanDetail == null) {
      return const EmptyWidget(text: '暂无商品信息');
    }

    return Column(
      children: [
        // 商品头部信息
        LoanDetailHeader(loanDetail: state.loanDetail!),
        SizedBox(height: 8),
        // 主要内容区域
        Expanded(
          child: Stack(
            children: [
              // 主内容区域
              Row(
                children: [
                  // 左侧导航栏（可能隐藏）
                  LoanDetailNavigation(
                    isExpanded: state.isNavigationExpanded,
                    navigationItems: state.navigationItems,
                    onToggle: notifier.toggleNavigation,
                    onNavigationTap: (key) => notifier.scrollToSection(key),
                  ),

                  // 右侧内容区域（自动撑满剩余空间）
                  Expanded(
                    child: LoanDetailScrollableContent(
                      state: state,
                      itemScrollController: notifier.itemScrollController,
                      itemPositionsListener: notifier.itemPositionsListener,
                      onItemPositionsChanged: notifier.onItemPositionsChanged,
                    ),
                  ),
                ],
              ),

              // 浮动导航按钮（固定在左上角）
              Positioned(
                top: 16,
                left: 16,
                child: _buildFloatingNavigationButton(notifier, state),
              ),
            ],
          ),
        ),
        
        // 底部联系客服按钮
        _buildBottomButton(notifier),
      ],
    );
  }

  Widget _buildBottomButton(notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: AppColors.dividerColor, width: 0.5),
        ),
      ),
      child: SafeArea(
        child: buildFilledButton(
          '联系客服',
          backgroundColor: AppColors.primary,
          fontColor: Colors.white,
          onPressed: () => notifier.contactCustomerService(),
        ),
      ),
    );
  }

  /// 构建浮动导航按钮
  Widget _buildFloatingNavigationButton(notifier, state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        elevation: 0,
        borderRadius: BorderRadius.circular(20),
        color: AppColors.primary,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: notifier.toggleNavigation,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.menu,
                  color: Colors.white,
                  size: 20,
                ),
                if (state.isNavigationExpanded) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.keyboard_arrow_right,
                    color: Colors.white,
                    size: 16,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
