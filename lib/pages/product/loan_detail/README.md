# 贷款商品详情页面

## 功能概述

贷款商品详情页面用于展示具体贷款产品的详细信息，包括产品基本信息、申请要求、准入条件等。页面采用左侧导航栏 + 右侧内容区域的布局设计，支持导航栏的展开/收起功能。

## 目录结构

```
lib/pages/product/loan_detail/
├── README.md                           # 说明文档
├── loan_detail_page.dart              # 主页面（使用Riverpod）
├── test_page.dart                     # 测试页面
├── api/
│   └── loan_detail_api.dart           # API接口
├── models/
│   └── loan_detail_models.dart        # 数据模型
├── provider/
│   ├── loan_detail_state.dart         # 状态定义
│   └── loan_detail_provider.dart      # 状态管理
└── widgets/
    ├── loan_detail_header.dart        # 头部组件
    ├── loan_detail_navigation.dart    # 导航栏组件
    └── loan_detail_content.dart       # 内容区域组件
```

## 主要功能

### 1. 商品信息展示
- 银行Logo和名称
- 产品名称
- 最高额度、年化利率、贷款期限、还款方式等关键信息

### 2. 导航功能
- **浮动导航切换按钮**：固定在左上角的圆角按钮
- **完全隐藏导航栏**：收起时宽度为0，内容区域撑满整个宽度
- **可滚动导航区域**：支持大量导航项的上下滚动
- 支持点击导航项滚动到对应内容区域
- **右侧滚动时左侧导航同步高亮**
- **动态导航项**：包括个人要求、企业要求、征信要求、准入地区、禁入行业、自定义要求

### 3. 详细要求展示
- 个人申请要求
- 企业申请要求
- 征信要求
- 准入地区限制
- 禁入行业列表
- 自定义申请流程

### 4. 交互功能
- 联系客服按钮
- 分享功能（待实现）
- 更多操作（待实现）

### 5. 数据结构优化
- **统一的ContentItem数据结构**，替代了原来的RequirementItem、NavigationItem、CustomRequireModel
- 简化了数据管理和状态同步

## 使用方法

### 页面跳转

```dart
// 跳转到贷款商品详情页面（简化版，推荐使用）
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => SimpleLoanDetailPage(
      productId: '17', // 商品ID
    ),
  ),
);

// 或者使用完整版（需要Riverpod支持）
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ProviderScope(
      child: LoanDetailPage(
        productId: '17', // 商品ID
      ),
    ),
  ),
);
```

### API接口

页面使用以下API接口获取数据：

```
GET /product/info/{productId}
```

接口返回的JSON数据会被解析为 `LoanDetailModel` 对象。

## 状态管理

使用Riverpod进行状态管理：

```dart
// 获取状态
final state = ref.watch(loanDetailNotifierProvider);

// 获取notifier
final notifier = ref.read(loanDetailNotifierProvider.notifier);

// 初始化数据
notifier.initialize(productId);

// 切换导航栏
notifier.toggleNavigation();

// 选择导航项
notifier.selectNavigation('personal');

// 滚动到指定区域
notifier.scrollToSection('company', scrollController);

// 重新加载
notifier.reload(productId);
```

## 样式定制

页面使用了项目中的AppColors颜色系统：
- `AppColors.primary`: 主色调（导航栏、按钮等）
- `AppColors.background`: 背景色
- `AppColors.textColor1/3/6`: 文本颜色
- `AppColors.dividerColor`: 分割线颜色

## 错误处理

- 网络请求失败时显示ErrorStatusWidget
- 数据为空时显示EmptyWidget
- 加载中显示LoadingWidget

## 版本说明

### 简化版页面（SimpleLoanDetailPage）
- **推荐使用**：功能完整，无复杂依赖
- 直接使用StatefulWidget管理状态
- 已实现所有UI功能和API调用
- 无需额外的代码生成步骤

### 完整版页面（LoanDetailPage）
- 使用Riverpod进行状态管理
- 使用Freezed生成数据模型
- 需要运行 `dart run build_runner build` 生成相关文件
- 更适合复杂的状态管理场景

## 注意事项

1. **推荐使用SimpleLoanDetailPage**，功能完整且无复杂依赖
2. 如需使用完整版，需要运行 `dart run build_runner build` 生成freezed和riverpod相关文件
3. 确保项目中已正确配置网络请求服务
4. 页面依赖项目的通用组件（ErrorStatusWidget、EmptyWidget、LoadingWidget）
5. **已移除ducafe_ui_core依赖**，使用标准Flutter组件

## 待完善功能

1. 联系客服功能的具体实现
2. 分享功能的实现
3. 更多操作功能的实现
4. 产品收藏功能
5. 申请跳转功能

## 扩展功能

如需添加更多功能，可以考虑：
- 产品对比功能
- 相关产品推荐
- 用户评价展示
- 申请进度跟踪
- 产品详情页面的SEO优化
