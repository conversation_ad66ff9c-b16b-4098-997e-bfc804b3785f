// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'loan_detail_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoanDetailModel {
  String get id;
  String? get cateId;
  String get bankId;
  String get adminId;
  String get deptId;
  String? get pic;
  String get name;
  String? get productSn;
  int get type;
  String? get keywords;
  String get bankName;
  int get minQuota;
  int get maxQuota;
  String get minAnnualRate;
  String get maxAnnualRate;
  String get loanTerm;
  String get repaymentType;
  String? get repaymentName;
  String? get advantageDataJson;
  int get loanType;
  String? get legalApplyRequire;
  String? get companyTaxInfo;
  String? get companyBizStatus;
  String? get companyBankFlow;
  String? get corporateAge;
  String? get corporateMaxAge;
  String? get corporateChangeTime;
  String? get corporateSharesRatio;
  String? get companyCreateTime;
  String? get companyTaxRating;
  String? get companyTaxStatus;
  String? get companyReceiptRequire;
  int get accessAreaType;
  int get publishStatus;
  String? get verifyStatus;
  String? get note;
  String get createTime;
  String? get updateBy;
  String? get matchType;
  String? get creditRequire;
  String? get otherRequire;
  String? get corporateRequire;
  String? get companyApplyRequire;
  String? get accessIndustry;
  String? get prohibitIndustry;
  String? get requiredMaterials;
  String? get applyDesc;
  String? get channelCode;
  String? get intoElement;
  List<ForbidIndustryModel> get forbidIndustryList;
  List<CustomRequireModel> get customRequireList;
  List<dynamic> get treeSelect;
  bool get haveQuestion;

  /// Create a copy of LoanDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoanDetailModelCopyWith<LoanDetailModel> get copyWith =>
      _$LoanDetailModelCopyWithImpl<LoanDetailModel>(
          this as LoanDetailModel, _$identity);

  /// Serializes this LoanDetailModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoanDetailModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cateId, cateId) || other.cateId == cateId) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            (identical(other.deptId, deptId) || other.deptId == deptId) &&
            (identical(other.pic, pic) || other.pic == pic) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.productSn, productSn) ||
                other.productSn == productSn) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.keywords, keywords) ||
                other.keywords == keywords) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.minQuota, minQuota) ||
                other.minQuota == minQuota) &&
            (identical(other.maxQuota, maxQuota) ||
                other.maxQuota == maxQuota) &&
            (identical(other.minAnnualRate, minAnnualRate) ||
                other.minAnnualRate == minAnnualRate) &&
            (identical(other.maxAnnualRate, maxAnnualRate) ||
                other.maxAnnualRate == maxAnnualRate) &&
            (identical(other.loanTerm, loanTerm) ||
                other.loanTerm == loanTerm) &&
            (identical(other.repaymentType, repaymentType) ||
                other.repaymentType == repaymentType) &&
            (identical(other.repaymentName, repaymentName) ||
                other.repaymentName == repaymentName) &&
            (identical(other.advantageDataJson, advantageDataJson) ||
                other.advantageDataJson == advantageDataJson) &&
            (identical(other.loanType, loanType) ||
                other.loanType == loanType) &&
            (identical(other.legalApplyRequire, legalApplyRequire) ||
                other.legalApplyRequire == legalApplyRequire) &&
            (identical(other.companyTaxInfo, companyTaxInfo) ||
                other.companyTaxInfo == companyTaxInfo) &&
            (identical(other.companyBizStatus, companyBizStatus) ||
                other.companyBizStatus == companyBizStatus) &&
            (identical(other.companyBankFlow, companyBankFlow) ||
                other.companyBankFlow == companyBankFlow) &&
            (identical(other.corporateAge, corporateAge) ||
                other.corporateAge == corporateAge) &&
            (identical(other.corporateMaxAge, corporateMaxAge) ||
                other.corporateMaxAge == corporateMaxAge) &&
            (identical(other.corporateChangeTime, corporateChangeTime) ||
                other.corporateChangeTime == corporateChangeTime) &&
            (identical(other.corporateSharesRatio, corporateSharesRatio) ||
                other.corporateSharesRatio == corporateSharesRatio) &&
            (identical(other.companyCreateTime, companyCreateTime) ||
                other.companyCreateTime == companyCreateTime) &&
            (identical(other.companyTaxRating, companyTaxRating) ||
                other.companyTaxRating == companyTaxRating) &&
            (identical(other.companyTaxStatus, companyTaxStatus) ||
                other.companyTaxStatus == companyTaxStatus) &&
            (identical(other.companyReceiptRequire, companyReceiptRequire) ||
                other.companyReceiptRequire == companyReceiptRequire) &&
            (identical(other.accessAreaType, accessAreaType) ||
                other.accessAreaType == accessAreaType) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.verifyStatus, verifyStatus) ||
                other.verifyStatus == verifyStatus) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            (identical(other.creditRequire, creditRequire) ||
                other.creditRequire == creditRequire) &&
            (identical(other.otherRequire, otherRequire) ||
                other.otherRequire == otherRequire) &&
            (identical(other.corporateRequire, corporateRequire) ||
                other.corporateRequire == corporateRequire) &&
            (identical(other.companyApplyRequire, companyApplyRequire) ||
                other.companyApplyRequire == companyApplyRequire) &&
            (identical(other.accessIndustry, accessIndustry) ||
                other.accessIndustry == accessIndustry) &&
            (identical(other.prohibitIndustry, prohibitIndustry) ||
                other.prohibitIndustry == prohibitIndustry) &&
            (identical(other.requiredMaterials, requiredMaterials) ||
                other.requiredMaterials == requiredMaterials) &&
            (identical(other.applyDesc, applyDesc) ||
                other.applyDesc == applyDesc) &&
            (identical(other.channelCode, channelCode) ||
                other.channelCode == channelCode) &&
            (identical(other.intoElement, intoElement) ||
                other.intoElement == intoElement) &&
            const DeepCollectionEquality()
                .equals(other.forbidIndustryList, forbidIndustryList) &&
            const DeepCollectionEquality()
                .equals(other.customRequireList, customRequireList) &&
            const DeepCollectionEquality()
                .equals(other.treeSelect, treeSelect) &&
            (identical(other.haveQuestion, haveQuestion) || other.haveQuestion == haveQuestion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        cateId,
        bankId,
        adminId,
        deptId,
        pic,
        name,
        productSn,
        type,
        keywords,
        bankName,
        minQuota,
        maxQuota,
        minAnnualRate,
        maxAnnualRate,
        loanTerm,
        repaymentType,
        repaymentName,
        advantageDataJson,
        loanType,
        legalApplyRequire,
        companyTaxInfo,
        companyBizStatus,
        companyBankFlow,
        corporateAge,
        corporateMaxAge,
        corporateChangeTime,
        corporateSharesRatio,
        companyCreateTime,
        companyTaxRating,
        companyTaxStatus,
        companyReceiptRequire,
        accessAreaType,
        publishStatus,
        verifyStatus,
        note,
        createTime,
        updateBy,
        matchType,
        creditRequire,
        otherRequire,
        corporateRequire,
        companyApplyRequire,
        accessIndustry,
        prohibitIndustry,
        requiredMaterials,
        applyDesc,
        channelCode,
        intoElement,
        const DeepCollectionEquality().hash(forbidIndustryList),
        const DeepCollectionEquality().hash(customRequireList),
        const DeepCollectionEquality().hash(treeSelect),
        haveQuestion
      ]);

  @override
  String toString() {
    return 'LoanDetailModel(id: $id, cateId: $cateId, bankId: $bankId, adminId: $adminId, deptId: $deptId, pic: $pic, name: $name, productSn: $productSn, type: $type, keywords: $keywords, bankName: $bankName, minQuota: $minQuota, maxQuota: $maxQuota, minAnnualRate: $minAnnualRate, maxAnnualRate: $maxAnnualRate, loanTerm: $loanTerm, repaymentType: $repaymentType, repaymentName: $repaymentName, advantageDataJson: $advantageDataJson, loanType: $loanType, legalApplyRequire: $legalApplyRequire, companyTaxInfo: $companyTaxInfo, companyBizStatus: $companyBizStatus, companyBankFlow: $companyBankFlow, corporateAge: $corporateAge, corporateMaxAge: $corporateMaxAge, corporateChangeTime: $corporateChangeTime, corporateSharesRatio: $corporateSharesRatio, companyCreateTime: $companyCreateTime, companyTaxRating: $companyTaxRating, companyTaxStatus: $companyTaxStatus, companyReceiptRequire: $companyReceiptRequire, accessAreaType: $accessAreaType, publishStatus: $publishStatus, verifyStatus: $verifyStatus, note: $note, createTime: $createTime, updateBy: $updateBy, matchType: $matchType, creditRequire: $creditRequire, otherRequire: $otherRequire, corporateRequire: $corporateRequire, companyApplyRequire: $companyApplyRequire, accessIndustry: $accessIndustry, prohibitIndustry: $prohibitIndustry, requiredMaterials: $requiredMaterials, applyDesc: $applyDesc, channelCode: $channelCode, intoElement: $intoElement, forbidIndustryList: $forbidIndustryList, customRequireList: $customRequireList, treeSelect: $treeSelect, haveQuestion: $haveQuestion)';
  }
}

/// @nodoc
abstract mixin class $LoanDetailModelCopyWith<$Res> {
  factory $LoanDetailModelCopyWith(
          LoanDetailModel value, $Res Function(LoanDetailModel) _then) =
      _$LoanDetailModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? cateId,
      String bankId,
      String adminId,
      String deptId,
      String? pic,
      String name,
      String? productSn,
      int type,
      String? keywords,
      String bankName,
      int minQuota,
      int maxQuota,
      String minAnnualRate,
      String maxAnnualRate,
      String loanTerm,
      String repaymentType,
      String? repaymentName,
      String? advantageDataJson,
      int loanType,
      String? legalApplyRequire,
      String? companyTaxInfo,
      String? companyBizStatus,
      String? companyBankFlow,
      String? corporateAge,
      String? corporateMaxAge,
      String? corporateChangeTime,
      String? corporateSharesRatio,
      String? companyCreateTime,
      String? companyTaxRating,
      String? companyTaxStatus,
      String? companyReceiptRequire,
      int accessAreaType,
      int publishStatus,
      String? verifyStatus,
      String? note,
      String createTime,
      String? updateBy,
      String? matchType,
      String? creditRequire,
      String? otherRequire,
      String? corporateRequire,
      String? companyApplyRequire,
      String? accessIndustry,
      String? prohibitIndustry,
      String? requiredMaterials,
      String? applyDesc,
      String? channelCode,
      String? intoElement,
      List<ForbidIndustryModel> forbidIndustryList,
      List<CustomRequireModel> customRequireList,
      List<dynamic> treeSelect,
      bool haveQuestion});
}

/// @nodoc
class _$LoanDetailModelCopyWithImpl<$Res>
    implements $LoanDetailModelCopyWith<$Res> {
  _$LoanDetailModelCopyWithImpl(this._self, this._then);

  final LoanDetailModel _self;
  final $Res Function(LoanDetailModel) _then;

  /// Create a copy of LoanDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cateId = freezed,
    Object? bankId = null,
    Object? adminId = null,
    Object? deptId = null,
    Object? pic = freezed,
    Object? name = null,
    Object? productSn = freezed,
    Object? type = null,
    Object? keywords = freezed,
    Object? bankName = null,
    Object? minQuota = null,
    Object? maxQuota = null,
    Object? minAnnualRate = null,
    Object? maxAnnualRate = null,
    Object? loanTerm = null,
    Object? repaymentType = null,
    Object? repaymentName = freezed,
    Object? advantageDataJson = freezed,
    Object? loanType = null,
    Object? legalApplyRequire = freezed,
    Object? companyTaxInfo = freezed,
    Object? companyBizStatus = freezed,
    Object? companyBankFlow = freezed,
    Object? corporateAge = freezed,
    Object? corporateMaxAge = freezed,
    Object? corporateChangeTime = freezed,
    Object? corporateSharesRatio = freezed,
    Object? companyCreateTime = freezed,
    Object? companyTaxRating = freezed,
    Object? companyTaxStatus = freezed,
    Object? companyReceiptRequire = freezed,
    Object? accessAreaType = null,
    Object? publishStatus = null,
    Object? verifyStatus = freezed,
    Object? note = freezed,
    Object? createTime = null,
    Object? updateBy = freezed,
    Object? matchType = freezed,
    Object? creditRequire = freezed,
    Object? otherRequire = freezed,
    Object? corporateRequire = freezed,
    Object? companyApplyRequire = freezed,
    Object? accessIndustry = freezed,
    Object? prohibitIndustry = freezed,
    Object? requiredMaterials = freezed,
    Object? applyDesc = freezed,
    Object? channelCode = freezed,
    Object? intoElement = freezed,
    Object? forbidIndustryList = null,
    Object? customRequireList = null,
    Object? treeSelect = null,
    Object? haveQuestion = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cateId: freezed == cateId
          ? _self.cateId
          : cateId // ignore: cast_nullable_to_non_nullable
              as String?,
      bankId: null == bankId
          ? _self.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _self.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
      deptId: null == deptId
          ? _self.deptId
          : deptId // ignore: cast_nullable_to_non_nullable
              as String,
      pic: freezed == pic
          ? _self.pic
          : pic // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      productSn: freezed == productSn
          ? _self.productSn
          : productSn // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      keywords: freezed == keywords
          ? _self.keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: null == bankName
          ? _self.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      minQuota: null == minQuota
          ? _self.minQuota
          : minQuota // ignore: cast_nullable_to_non_nullable
              as int,
      maxQuota: null == maxQuota
          ? _self.maxQuota
          : maxQuota // ignore: cast_nullable_to_non_nullable
              as int,
      minAnnualRate: null == minAnnualRate
          ? _self.minAnnualRate
          : minAnnualRate // ignore: cast_nullable_to_non_nullable
              as String,
      maxAnnualRate: null == maxAnnualRate
          ? _self.maxAnnualRate
          : maxAnnualRate // ignore: cast_nullable_to_non_nullable
              as String,
      loanTerm: null == loanTerm
          ? _self.loanTerm
          : loanTerm // ignore: cast_nullable_to_non_nullable
              as String,
      repaymentType: null == repaymentType
          ? _self.repaymentType
          : repaymentType // ignore: cast_nullable_to_non_nullable
              as String,
      repaymentName: freezed == repaymentName
          ? _self.repaymentName
          : repaymentName // ignore: cast_nullable_to_non_nullable
              as String?,
      advantageDataJson: freezed == advantageDataJson
          ? _self.advantageDataJson
          : advantageDataJson // ignore: cast_nullable_to_non_nullable
              as String?,
      loanType: null == loanType
          ? _self.loanType
          : loanType // ignore: cast_nullable_to_non_nullable
              as int,
      legalApplyRequire: freezed == legalApplyRequire
          ? _self.legalApplyRequire
          : legalApplyRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      companyTaxInfo: freezed == companyTaxInfo
          ? _self.companyTaxInfo
          : companyTaxInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      companyBizStatus: freezed == companyBizStatus
          ? _self.companyBizStatus
          : companyBizStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      companyBankFlow: freezed == companyBankFlow
          ? _self.companyBankFlow
          : companyBankFlow // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateAge: freezed == corporateAge
          ? _self.corporateAge
          : corporateAge // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateMaxAge: freezed == corporateMaxAge
          ? _self.corporateMaxAge
          : corporateMaxAge // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateChangeTime: freezed == corporateChangeTime
          ? _self.corporateChangeTime
          : corporateChangeTime // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateSharesRatio: freezed == corporateSharesRatio
          ? _self.corporateSharesRatio
          : corporateSharesRatio // ignore: cast_nullable_to_non_nullable
              as String?,
      companyCreateTime: freezed == companyCreateTime
          ? _self.companyCreateTime
          : companyCreateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      companyTaxRating: freezed == companyTaxRating
          ? _self.companyTaxRating
          : companyTaxRating // ignore: cast_nullable_to_non_nullable
              as String?,
      companyTaxStatus: freezed == companyTaxStatus
          ? _self.companyTaxStatus
          : companyTaxStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      companyReceiptRequire: freezed == companyReceiptRequire
          ? _self.companyReceiptRequire
          : companyReceiptRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      accessAreaType: null == accessAreaType
          ? _self.accessAreaType
          : accessAreaType // ignore: cast_nullable_to_non_nullable
              as int,
      publishStatus: null == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as int,
      verifyStatus: freezed == verifyStatus
          ? _self.verifyStatus
          : verifyStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateBy: freezed == updateBy
          ? _self.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as String?,
      matchType: freezed == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as String?,
      creditRequire: freezed == creditRequire
          ? _self.creditRequire
          : creditRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      otherRequire: freezed == otherRequire
          ? _self.otherRequire
          : otherRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateRequire: freezed == corporateRequire
          ? _self.corporateRequire
          : corporateRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      companyApplyRequire: freezed == companyApplyRequire
          ? _self.companyApplyRequire
          : companyApplyRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      accessIndustry: freezed == accessIndustry
          ? _self.accessIndustry
          : accessIndustry // ignore: cast_nullable_to_non_nullable
              as String?,
      prohibitIndustry: freezed == prohibitIndustry
          ? _self.prohibitIndustry
          : prohibitIndustry // ignore: cast_nullable_to_non_nullable
              as String?,
      requiredMaterials: freezed == requiredMaterials
          ? _self.requiredMaterials
          : requiredMaterials // ignore: cast_nullable_to_non_nullable
              as String?,
      applyDesc: freezed == applyDesc
          ? _self.applyDesc
          : applyDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      channelCode: freezed == channelCode
          ? _self.channelCode
          : channelCode // ignore: cast_nullable_to_non_nullable
              as String?,
      intoElement: freezed == intoElement
          ? _self.intoElement
          : intoElement // ignore: cast_nullable_to_non_nullable
              as String?,
      forbidIndustryList: null == forbidIndustryList
          ? _self.forbidIndustryList
          : forbidIndustryList // ignore: cast_nullable_to_non_nullable
              as List<ForbidIndustryModel>,
      customRequireList: null == customRequireList
          ? _self.customRequireList
          : customRequireList // ignore: cast_nullable_to_non_nullable
              as List<CustomRequireModel>,
      treeSelect: null == treeSelect
          ? _self.treeSelect
          : treeSelect // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      haveQuestion: null == haveQuestion
          ? _self.haveQuestion
          : haveQuestion // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LoanDetailModel implements LoanDetailModel {
  const _LoanDetailModel(
      {required this.id,
      this.cateId,
      required this.bankId,
      required this.adminId,
      required this.deptId,
      this.pic,
      required this.name,
      this.productSn,
      required this.type,
      this.keywords,
      required this.bankName,
      required this.minQuota,
      required this.maxQuota,
      required this.minAnnualRate,
      required this.maxAnnualRate,
      required this.loanTerm,
      required this.repaymentType,
      this.repaymentName,
      this.advantageDataJson,
      required this.loanType,
      this.legalApplyRequire,
      this.companyTaxInfo,
      this.companyBizStatus,
      this.companyBankFlow,
      this.corporateAge,
      this.corporateMaxAge,
      this.corporateChangeTime,
      this.corporateSharesRatio,
      this.companyCreateTime,
      this.companyTaxRating,
      this.companyTaxStatus,
      this.companyReceiptRequire,
      required this.accessAreaType,
      required this.publishStatus,
      this.verifyStatus,
      this.note,
      required this.createTime,
      this.updateBy,
      this.matchType,
      this.creditRequire,
      this.otherRequire,
      this.corporateRequire,
      this.companyApplyRequire,
      this.accessIndustry,
      this.prohibitIndustry,
      this.requiredMaterials,
      this.applyDesc,
      this.channelCode,
      this.intoElement,
      final List<ForbidIndustryModel> forbidIndustryList = const [],
      final List<CustomRequireModel> customRequireList = const [],
      final List<dynamic> treeSelect = const [],
      this.haveQuestion = false})
      : _forbidIndustryList = forbidIndustryList,
        _customRequireList = customRequireList,
        _treeSelect = treeSelect;
  factory _LoanDetailModel.fromJson(Map<String, dynamic> json) =>
      _$LoanDetailModelFromJson(json);

  @override
  final String id;
  @override
  final String? cateId;
  @override
  final String bankId;
  @override
  final String adminId;
  @override
  final String deptId;
  @override
  final String? pic;
  @override
  final String name;
  @override
  final String? productSn;
  @override
  final int type;
  @override
  final String? keywords;
  @override
  final String bankName;
  @override
  final int minQuota;
  @override
  final int maxQuota;
  @override
  final String minAnnualRate;
  @override
  final String maxAnnualRate;
  @override
  final String loanTerm;
  @override
  final String repaymentType;
  @override
  final String? repaymentName;
  @override
  final String? advantageDataJson;
  @override
  final int loanType;
  @override
  final String? legalApplyRequire;
  @override
  final String? companyTaxInfo;
  @override
  final String? companyBizStatus;
  @override
  final String? companyBankFlow;
  @override
  final String? corporateAge;
  @override
  final String? corporateMaxAge;
  @override
  final String? corporateChangeTime;
  @override
  final String? corporateSharesRatio;
  @override
  final String? companyCreateTime;
  @override
  final String? companyTaxRating;
  @override
  final String? companyTaxStatus;
  @override
  final String? companyReceiptRequire;
  @override
  final int accessAreaType;
  @override
  final int publishStatus;
  @override
  final String? verifyStatus;
  @override
  final String? note;
  @override
  final String createTime;
  @override
  final String? updateBy;
  @override
  final String? matchType;
  @override
  final String? creditRequire;
  @override
  final String? otherRequire;
  @override
  final String? corporateRequire;
  @override
  final String? companyApplyRequire;
  @override
  final String? accessIndustry;
  @override
  final String? prohibitIndustry;
  @override
  final String? requiredMaterials;
  @override
  final String? applyDesc;
  @override
  final String? channelCode;
  @override
  final String? intoElement;
  final List<ForbidIndustryModel> _forbidIndustryList;
  @override
  @JsonKey()
  List<ForbidIndustryModel> get forbidIndustryList {
    if (_forbidIndustryList is EqualUnmodifiableListView)
      return _forbidIndustryList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_forbidIndustryList);
  }

  final List<CustomRequireModel> _customRequireList;
  @override
  @JsonKey()
  List<CustomRequireModel> get customRequireList {
    if (_customRequireList is EqualUnmodifiableListView)
      return _customRequireList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customRequireList);
  }

  final List<dynamic> _treeSelect;
  @override
  @JsonKey()
  List<dynamic> get treeSelect {
    if (_treeSelect is EqualUnmodifiableListView) return _treeSelect;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_treeSelect);
  }

  @override
  @JsonKey()
  final bool haveQuestion;

  /// Create a copy of LoanDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoanDetailModelCopyWith<_LoanDetailModel> get copyWith =>
      __$LoanDetailModelCopyWithImpl<_LoanDetailModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LoanDetailModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoanDetailModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cateId, cateId) || other.cateId == cateId) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            (identical(other.deptId, deptId) || other.deptId == deptId) &&
            (identical(other.pic, pic) || other.pic == pic) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.productSn, productSn) ||
                other.productSn == productSn) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.keywords, keywords) ||
                other.keywords == keywords) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.minQuota, minQuota) ||
                other.minQuota == minQuota) &&
            (identical(other.maxQuota, maxQuota) ||
                other.maxQuota == maxQuota) &&
            (identical(other.minAnnualRate, minAnnualRate) ||
                other.minAnnualRate == minAnnualRate) &&
            (identical(other.maxAnnualRate, maxAnnualRate) ||
                other.maxAnnualRate == maxAnnualRate) &&
            (identical(other.loanTerm, loanTerm) ||
                other.loanTerm == loanTerm) &&
            (identical(other.repaymentType, repaymentType) ||
                other.repaymentType == repaymentType) &&
            (identical(other.repaymentName, repaymentName) ||
                other.repaymentName == repaymentName) &&
            (identical(other.advantageDataJson, advantageDataJson) ||
                other.advantageDataJson == advantageDataJson) &&
            (identical(other.loanType, loanType) ||
                other.loanType == loanType) &&
            (identical(other.legalApplyRequire, legalApplyRequire) ||
                other.legalApplyRequire == legalApplyRequire) &&
            (identical(other.companyTaxInfo, companyTaxInfo) ||
                other.companyTaxInfo == companyTaxInfo) &&
            (identical(other.companyBizStatus, companyBizStatus) ||
                other.companyBizStatus == companyBizStatus) &&
            (identical(other.companyBankFlow, companyBankFlow) ||
                other.companyBankFlow == companyBankFlow) &&
            (identical(other.corporateAge, corporateAge) ||
                other.corporateAge == corporateAge) &&
            (identical(other.corporateMaxAge, corporateMaxAge) ||
                other.corporateMaxAge == corporateMaxAge) &&
            (identical(other.corporateChangeTime, corporateChangeTime) ||
                other.corporateChangeTime == corporateChangeTime) &&
            (identical(other.corporateSharesRatio, corporateSharesRatio) ||
                other.corporateSharesRatio == corporateSharesRatio) &&
            (identical(other.companyCreateTime, companyCreateTime) ||
                other.companyCreateTime == companyCreateTime) &&
            (identical(other.companyTaxRating, companyTaxRating) ||
                other.companyTaxRating == companyTaxRating) &&
            (identical(other.companyTaxStatus, companyTaxStatus) ||
                other.companyTaxStatus == companyTaxStatus) &&
            (identical(other.companyReceiptRequire, companyReceiptRequire) ||
                other.companyReceiptRequire == companyReceiptRequire) &&
            (identical(other.accessAreaType, accessAreaType) ||
                other.accessAreaType == accessAreaType) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.verifyStatus, verifyStatus) ||
                other.verifyStatus == verifyStatus) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.matchType, matchType) ||
                other.matchType == matchType) &&
            (identical(other.creditRequire, creditRequire) ||
                other.creditRequire == creditRequire) &&
            (identical(other.otherRequire, otherRequire) ||
                other.otherRequire == otherRequire) &&
            (identical(other.corporateRequire, corporateRequire) ||
                other.corporateRequire == corporateRequire) &&
            (identical(other.companyApplyRequire, companyApplyRequire) ||
                other.companyApplyRequire == companyApplyRequire) &&
            (identical(other.accessIndustry, accessIndustry) ||
                other.accessIndustry == accessIndustry) &&
            (identical(other.prohibitIndustry, prohibitIndustry) ||
                other.prohibitIndustry == prohibitIndustry) &&
            (identical(other.requiredMaterials, requiredMaterials) ||
                other.requiredMaterials == requiredMaterials) &&
            (identical(other.applyDesc, applyDesc) ||
                other.applyDesc == applyDesc) &&
            (identical(other.channelCode, channelCode) ||
                other.channelCode == channelCode) &&
            (identical(other.intoElement, intoElement) ||
                other.intoElement == intoElement) &&
            const DeepCollectionEquality()
                .equals(other._forbidIndustryList, _forbidIndustryList) &&
            const DeepCollectionEquality()
                .equals(other._customRequireList, _customRequireList) &&
            const DeepCollectionEquality()
                .equals(other._treeSelect, _treeSelect) &&
            (identical(other.haveQuestion, haveQuestion) || other.haveQuestion == haveQuestion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        cateId,
        bankId,
        adminId,
        deptId,
        pic,
        name,
        productSn,
        type,
        keywords,
        bankName,
        minQuota,
        maxQuota,
        minAnnualRate,
        maxAnnualRate,
        loanTerm,
        repaymentType,
        repaymentName,
        advantageDataJson,
        loanType,
        legalApplyRequire,
        companyTaxInfo,
        companyBizStatus,
        companyBankFlow,
        corporateAge,
        corporateMaxAge,
        corporateChangeTime,
        corporateSharesRatio,
        companyCreateTime,
        companyTaxRating,
        companyTaxStatus,
        companyReceiptRequire,
        accessAreaType,
        publishStatus,
        verifyStatus,
        note,
        createTime,
        updateBy,
        matchType,
        creditRequire,
        otherRequire,
        corporateRequire,
        companyApplyRequire,
        accessIndustry,
        prohibitIndustry,
        requiredMaterials,
        applyDesc,
        channelCode,
        intoElement,
        const DeepCollectionEquality().hash(_forbidIndustryList),
        const DeepCollectionEquality().hash(_customRequireList),
        const DeepCollectionEquality().hash(_treeSelect),
        haveQuestion
      ]);

  @override
  String toString() {
    return 'LoanDetailModel(id: $id, cateId: $cateId, bankId: $bankId, adminId: $adminId, deptId: $deptId, pic: $pic, name: $name, productSn: $productSn, type: $type, keywords: $keywords, bankName: $bankName, minQuota: $minQuota, maxQuota: $maxQuota, minAnnualRate: $minAnnualRate, maxAnnualRate: $maxAnnualRate, loanTerm: $loanTerm, repaymentType: $repaymentType, repaymentName: $repaymentName, advantageDataJson: $advantageDataJson, loanType: $loanType, legalApplyRequire: $legalApplyRequire, companyTaxInfo: $companyTaxInfo, companyBizStatus: $companyBizStatus, companyBankFlow: $companyBankFlow, corporateAge: $corporateAge, corporateMaxAge: $corporateMaxAge, corporateChangeTime: $corporateChangeTime, corporateSharesRatio: $corporateSharesRatio, companyCreateTime: $companyCreateTime, companyTaxRating: $companyTaxRating, companyTaxStatus: $companyTaxStatus, companyReceiptRequire: $companyReceiptRequire, accessAreaType: $accessAreaType, publishStatus: $publishStatus, verifyStatus: $verifyStatus, note: $note, createTime: $createTime, updateBy: $updateBy, matchType: $matchType, creditRequire: $creditRequire, otherRequire: $otherRequire, corporateRequire: $corporateRequire, companyApplyRequire: $companyApplyRequire, accessIndustry: $accessIndustry, prohibitIndustry: $prohibitIndustry, requiredMaterials: $requiredMaterials, applyDesc: $applyDesc, channelCode: $channelCode, intoElement: $intoElement, forbidIndustryList: $forbidIndustryList, customRequireList: $customRequireList, treeSelect: $treeSelect, haveQuestion: $haveQuestion)';
  }
}

/// @nodoc
abstract mixin class _$LoanDetailModelCopyWith<$Res>
    implements $LoanDetailModelCopyWith<$Res> {
  factory _$LoanDetailModelCopyWith(
          _LoanDetailModel value, $Res Function(_LoanDetailModel) _then) =
      __$LoanDetailModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? cateId,
      String bankId,
      String adminId,
      String deptId,
      String? pic,
      String name,
      String? productSn,
      int type,
      String? keywords,
      String bankName,
      int minQuota,
      int maxQuota,
      String minAnnualRate,
      String maxAnnualRate,
      String loanTerm,
      String repaymentType,
      String? repaymentName,
      String? advantageDataJson,
      int loanType,
      String? legalApplyRequire,
      String? companyTaxInfo,
      String? companyBizStatus,
      String? companyBankFlow,
      String? corporateAge,
      String? corporateMaxAge,
      String? corporateChangeTime,
      String? corporateSharesRatio,
      String? companyCreateTime,
      String? companyTaxRating,
      String? companyTaxStatus,
      String? companyReceiptRequire,
      int accessAreaType,
      int publishStatus,
      String? verifyStatus,
      String? note,
      String createTime,
      String? updateBy,
      String? matchType,
      String? creditRequire,
      String? otherRequire,
      String? corporateRequire,
      String? companyApplyRequire,
      String? accessIndustry,
      String? prohibitIndustry,
      String? requiredMaterials,
      String? applyDesc,
      String? channelCode,
      String? intoElement,
      List<ForbidIndustryModel> forbidIndustryList,
      List<CustomRequireModel> customRequireList,
      List<dynamic> treeSelect,
      bool haveQuestion});
}

/// @nodoc
class __$LoanDetailModelCopyWithImpl<$Res>
    implements _$LoanDetailModelCopyWith<$Res> {
  __$LoanDetailModelCopyWithImpl(this._self, this._then);

  final _LoanDetailModel _self;
  final $Res Function(_LoanDetailModel) _then;

  /// Create a copy of LoanDetailModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? cateId = freezed,
    Object? bankId = null,
    Object? adminId = null,
    Object? deptId = null,
    Object? pic = freezed,
    Object? name = null,
    Object? productSn = freezed,
    Object? type = null,
    Object? keywords = freezed,
    Object? bankName = null,
    Object? minQuota = null,
    Object? maxQuota = null,
    Object? minAnnualRate = null,
    Object? maxAnnualRate = null,
    Object? loanTerm = null,
    Object? repaymentType = null,
    Object? repaymentName = freezed,
    Object? advantageDataJson = freezed,
    Object? loanType = null,
    Object? legalApplyRequire = freezed,
    Object? companyTaxInfo = freezed,
    Object? companyBizStatus = freezed,
    Object? companyBankFlow = freezed,
    Object? corporateAge = freezed,
    Object? corporateMaxAge = freezed,
    Object? corporateChangeTime = freezed,
    Object? corporateSharesRatio = freezed,
    Object? companyCreateTime = freezed,
    Object? companyTaxRating = freezed,
    Object? companyTaxStatus = freezed,
    Object? companyReceiptRequire = freezed,
    Object? accessAreaType = null,
    Object? publishStatus = null,
    Object? verifyStatus = freezed,
    Object? note = freezed,
    Object? createTime = null,
    Object? updateBy = freezed,
    Object? matchType = freezed,
    Object? creditRequire = freezed,
    Object? otherRequire = freezed,
    Object? corporateRequire = freezed,
    Object? companyApplyRequire = freezed,
    Object? accessIndustry = freezed,
    Object? prohibitIndustry = freezed,
    Object? requiredMaterials = freezed,
    Object? applyDesc = freezed,
    Object? channelCode = freezed,
    Object? intoElement = freezed,
    Object? forbidIndustryList = null,
    Object? customRequireList = null,
    Object? treeSelect = null,
    Object? haveQuestion = null,
  }) {
    return _then(_LoanDetailModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cateId: freezed == cateId
          ? _self.cateId
          : cateId // ignore: cast_nullable_to_non_nullable
              as String?,
      bankId: null == bankId
          ? _self.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _self.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
      deptId: null == deptId
          ? _self.deptId
          : deptId // ignore: cast_nullable_to_non_nullable
              as String,
      pic: freezed == pic
          ? _self.pic
          : pic // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      productSn: freezed == productSn
          ? _self.productSn
          : productSn // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
      keywords: freezed == keywords
          ? _self.keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: null == bankName
          ? _self.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String,
      minQuota: null == minQuota
          ? _self.minQuota
          : minQuota // ignore: cast_nullable_to_non_nullable
              as int,
      maxQuota: null == maxQuota
          ? _self.maxQuota
          : maxQuota // ignore: cast_nullable_to_non_nullable
              as int,
      minAnnualRate: null == minAnnualRate
          ? _self.minAnnualRate
          : minAnnualRate // ignore: cast_nullable_to_non_nullable
              as String,
      maxAnnualRate: null == maxAnnualRate
          ? _self.maxAnnualRate
          : maxAnnualRate // ignore: cast_nullable_to_non_nullable
              as String,
      loanTerm: null == loanTerm
          ? _self.loanTerm
          : loanTerm // ignore: cast_nullable_to_non_nullable
              as String,
      repaymentType: null == repaymentType
          ? _self.repaymentType
          : repaymentType // ignore: cast_nullable_to_non_nullable
              as String,
      repaymentName: freezed == repaymentName
          ? _self.repaymentName
          : repaymentName // ignore: cast_nullable_to_non_nullable
              as String?,
      advantageDataJson: freezed == advantageDataJson
          ? _self.advantageDataJson
          : advantageDataJson // ignore: cast_nullable_to_non_nullable
              as String?,
      loanType: null == loanType
          ? _self.loanType
          : loanType // ignore: cast_nullable_to_non_nullable
              as int,
      legalApplyRequire: freezed == legalApplyRequire
          ? _self.legalApplyRequire
          : legalApplyRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      companyTaxInfo: freezed == companyTaxInfo
          ? _self.companyTaxInfo
          : companyTaxInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      companyBizStatus: freezed == companyBizStatus
          ? _self.companyBizStatus
          : companyBizStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      companyBankFlow: freezed == companyBankFlow
          ? _self.companyBankFlow
          : companyBankFlow // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateAge: freezed == corporateAge
          ? _self.corporateAge
          : corporateAge // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateMaxAge: freezed == corporateMaxAge
          ? _self.corporateMaxAge
          : corporateMaxAge // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateChangeTime: freezed == corporateChangeTime
          ? _self.corporateChangeTime
          : corporateChangeTime // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateSharesRatio: freezed == corporateSharesRatio
          ? _self.corporateSharesRatio
          : corporateSharesRatio // ignore: cast_nullable_to_non_nullable
              as String?,
      companyCreateTime: freezed == companyCreateTime
          ? _self.companyCreateTime
          : companyCreateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      companyTaxRating: freezed == companyTaxRating
          ? _self.companyTaxRating
          : companyTaxRating // ignore: cast_nullable_to_non_nullable
              as String?,
      companyTaxStatus: freezed == companyTaxStatus
          ? _self.companyTaxStatus
          : companyTaxStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      companyReceiptRequire: freezed == companyReceiptRequire
          ? _self.companyReceiptRequire
          : companyReceiptRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      accessAreaType: null == accessAreaType
          ? _self.accessAreaType
          : accessAreaType // ignore: cast_nullable_to_non_nullable
              as int,
      publishStatus: null == publishStatus
          ? _self.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as int,
      verifyStatus: freezed == verifyStatus
          ? _self.verifyStatus
          : verifyStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: null == createTime
          ? _self.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String,
      updateBy: freezed == updateBy
          ? _self.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as String?,
      matchType: freezed == matchType
          ? _self.matchType
          : matchType // ignore: cast_nullable_to_non_nullable
              as String?,
      creditRequire: freezed == creditRequire
          ? _self.creditRequire
          : creditRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      otherRequire: freezed == otherRequire
          ? _self.otherRequire
          : otherRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      corporateRequire: freezed == corporateRequire
          ? _self.corporateRequire
          : corporateRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      companyApplyRequire: freezed == companyApplyRequire
          ? _self.companyApplyRequire
          : companyApplyRequire // ignore: cast_nullable_to_non_nullable
              as String?,
      accessIndustry: freezed == accessIndustry
          ? _self.accessIndustry
          : accessIndustry // ignore: cast_nullable_to_non_nullable
              as String?,
      prohibitIndustry: freezed == prohibitIndustry
          ? _self.prohibitIndustry
          : prohibitIndustry // ignore: cast_nullable_to_non_nullable
              as String?,
      requiredMaterials: freezed == requiredMaterials
          ? _self.requiredMaterials
          : requiredMaterials // ignore: cast_nullable_to_non_nullable
              as String?,
      applyDesc: freezed == applyDesc
          ? _self.applyDesc
          : applyDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      channelCode: freezed == channelCode
          ? _self.channelCode
          : channelCode // ignore: cast_nullable_to_non_nullable
              as String?,
      intoElement: freezed == intoElement
          ? _self.intoElement
          : intoElement // ignore: cast_nullable_to_non_nullable
              as String?,
      forbidIndustryList: null == forbidIndustryList
          ? _self._forbidIndustryList
          : forbidIndustryList // ignore: cast_nullable_to_non_nullable
              as List<ForbidIndustryModel>,
      customRequireList: null == customRequireList
          ? _self._customRequireList
          : customRequireList // ignore: cast_nullable_to_non_nullable
              as List<CustomRequireModel>,
      treeSelect: null == treeSelect
          ? _self._treeSelect
          : treeSelect // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      haveQuestion: null == haveQuestion
          ? _self.haveQuestion
          : haveQuestion // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$ForbidIndustryModel {
  String get id;
  String get industryId;
  String get industryCode;
  String get industryName;
  String get relation;
  int get type;

  /// Create a copy of ForbidIndustryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ForbidIndustryModelCopyWith<ForbidIndustryModel> get copyWith =>
      _$ForbidIndustryModelCopyWithImpl<ForbidIndustryModel>(
          this as ForbidIndustryModel, _$identity);

  /// Serializes this ForbidIndustryModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ForbidIndustryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.industryId, industryId) ||
                other.industryId == industryId) &&
            (identical(other.industryCode, industryCode) ||
                other.industryCode == industryCode) &&
            (identical(other.industryName, industryName) ||
                other.industryName == industryName) &&
            (identical(other.relation, relation) ||
                other.relation == relation) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, industryId, industryCode, industryName, relation, type);

  @override
  String toString() {
    return 'ForbidIndustryModel(id: $id, industryId: $industryId, industryCode: $industryCode, industryName: $industryName, relation: $relation, type: $type)';
  }
}

/// @nodoc
abstract mixin class $ForbidIndustryModelCopyWith<$Res> {
  factory $ForbidIndustryModelCopyWith(
          ForbidIndustryModel value, $Res Function(ForbidIndustryModel) _then) =
      _$ForbidIndustryModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String industryId,
      String industryCode,
      String industryName,
      String relation,
      int type});
}

/// @nodoc
class _$ForbidIndustryModelCopyWithImpl<$Res>
    implements $ForbidIndustryModelCopyWith<$Res> {
  _$ForbidIndustryModelCopyWithImpl(this._self, this._then);

  final ForbidIndustryModel _self;
  final $Res Function(ForbidIndustryModel) _then;

  /// Create a copy of ForbidIndustryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? industryId = null,
    Object? industryCode = null,
    Object? industryName = null,
    Object? relation = null,
    Object? type = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      industryId: null == industryId
          ? _self.industryId
          : industryId // ignore: cast_nullable_to_non_nullable
              as String,
      industryCode: null == industryCode
          ? _self.industryCode
          : industryCode // ignore: cast_nullable_to_non_nullable
              as String,
      industryName: null == industryName
          ? _self.industryName
          : industryName // ignore: cast_nullable_to_non_nullable
              as String,
      relation: null == relation
          ? _self.relation
          : relation // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ForbidIndustryModel implements ForbidIndustryModel {
  const _ForbidIndustryModel(
      {required this.id,
      required this.industryId,
      required this.industryCode,
      required this.industryName,
      required this.relation,
      required this.type});
  factory _ForbidIndustryModel.fromJson(Map<String, dynamic> json) =>
      _$ForbidIndustryModelFromJson(json);

  @override
  final String id;
  @override
  final String industryId;
  @override
  final String industryCode;
  @override
  final String industryName;
  @override
  final String relation;
  @override
  final int type;

  /// Create a copy of ForbidIndustryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ForbidIndustryModelCopyWith<_ForbidIndustryModel> get copyWith =>
      __$ForbidIndustryModelCopyWithImpl<_ForbidIndustryModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ForbidIndustryModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ForbidIndustryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.industryId, industryId) ||
                other.industryId == industryId) &&
            (identical(other.industryCode, industryCode) ||
                other.industryCode == industryCode) &&
            (identical(other.industryName, industryName) ||
                other.industryName == industryName) &&
            (identical(other.relation, relation) ||
                other.relation == relation) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, industryId, industryCode, industryName, relation, type);

  @override
  String toString() {
    return 'ForbidIndustryModel(id: $id, industryId: $industryId, industryCode: $industryCode, industryName: $industryName, relation: $relation, type: $type)';
  }
}

/// @nodoc
abstract mixin class _$ForbidIndustryModelCopyWith<$Res>
    implements $ForbidIndustryModelCopyWith<$Res> {
  factory _$ForbidIndustryModelCopyWith(_ForbidIndustryModel value,
          $Res Function(_ForbidIndustryModel) _then) =
      __$ForbidIndustryModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String industryId,
      String industryCode,
      String industryName,
      String relation,
      int type});
}

/// @nodoc
class __$ForbidIndustryModelCopyWithImpl<$Res>
    implements _$ForbidIndustryModelCopyWith<$Res> {
  __$ForbidIndustryModelCopyWithImpl(this._self, this._then);

  final _ForbidIndustryModel _self;
  final $Res Function(_ForbidIndustryModel) _then;

  /// Create a copy of ForbidIndustryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? industryId = null,
    Object? industryCode = null,
    Object? industryName = null,
    Object? relation = null,
    Object? type = null,
  }) {
    return _then(_ForbidIndustryModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      industryId: null == industryId
          ? _self.industryId
          : industryId // ignore: cast_nullable_to_non_nullable
              as String,
      industryCode: null == industryCode
          ? _self.industryCode
          : industryCode // ignore: cast_nullable_to_non_nullable
              as String,
      industryName: null == industryName
          ? _self.industryName
          : industryName // ignore: cast_nullable_to_non_nullable
              as String,
      relation: null == relation
          ? _self.relation
          : relation // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$CustomRequireModel {
  String get title;
  String get content;

  /// Create a copy of CustomRequireModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CustomRequireModelCopyWith<CustomRequireModel> get copyWith =>
      _$CustomRequireModelCopyWithImpl<CustomRequireModel>(
          this as CustomRequireModel, _$identity);

  /// Serializes this CustomRequireModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CustomRequireModel &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, content);

  @override
  String toString() {
    return 'CustomRequireModel(title: $title, content: $content)';
  }
}

/// @nodoc
abstract mixin class $CustomRequireModelCopyWith<$Res> {
  factory $CustomRequireModelCopyWith(
          CustomRequireModel value, $Res Function(CustomRequireModel) _then) =
      _$CustomRequireModelCopyWithImpl;
  @useResult
  $Res call({String title, String content});
}

/// @nodoc
class _$CustomRequireModelCopyWithImpl<$Res>
    implements $CustomRequireModelCopyWith<$Res> {
  _$CustomRequireModelCopyWithImpl(this._self, this._then);

  final CustomRequireModel _self;
  final $Res Function(CustomRequireModel) _then;

  /// Create a copy of CustomRequireModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? content = null,
  }) {
    return _then(_self.copyWith(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CustomRequireModel implements CustomRequireModel {
  const _CustomRequireModel({required this.title, required this.content});
  factory _CustomRequireModel.fromJson(Map<String, dynamic> json) =>
      _$CustomRequireModelFromJson(json);

  @override
  final String title;
  @override
  final String content;

  /// Create a copy of CustomRequireModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CustomRequireModelCopyWith<_CustomRequireModel> get copyWith =>
      __$CustomRequireModelCopyWithImpl<_CustomRequireModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CustomRequireModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CustomRequireModel &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, content);

  @override
  String toString() {
    return 'CustomRequireModel(title: $title, content: $content)';
  }
}

/// @nodoc
abstract mixin class _$CustomRequireModelCopyWith<$Res>
    implements $CustomRequireModelCopyWith<$Res> {
  factory _$CustomRequireModelCopyWith(
          _CustomRequireModel value, $Res Function(_CustomRequireModel) _then) =
      __$CustomRequireModelCopyWithImpl;
  @override
  @useResult
  $Res call({String title, String content});
}

/// @nodoc
class __$CustomRequireModelCopyWithImpl<$Res>
    implements _$CustomRequireModelCopyWith<$Res> {
  __$CustomRequireModelCopyWithImpl(this._self, this._then);

  final _CustomRequireModel _self;
  final $Res Function(_CustomRequireModel) _then;

  /// Create a copy of CustomRequireModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? content = null,
  }) {
    return _then(_CustomRequireModel(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$NavigationItem {
  String get title;
  String get key;
  bool get isSelected;

  /// Create a copy of NavigationItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NavigationItemCopyWith<NavigationItem> get copyWith =>
      _$NavigationItemCopyWithImpl<NavigationItem>(
          this as NavigationItem, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NavigationItem &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected));
  }

  @override
  int get hashCode => Object.hash(runtimeType, title, key, isSelected);

  @override
  String toString() {
    return 'NavigationItem(title: $title, key: $key, isSelected: $isSelected)';
  }
}

/// @nodoc
abstract mixin class $NavigationItemCopyWith<$Res> {
  factory $NavigationItemCopyWith(
          NavigationItem value, $Res Function(NavigationItem) _then) =
      _$NavigationItemCopyWithImpl;
  @useResult
  $Res call({String title, String key, bool isSelected});
}

/// @nodoc
class _$NavigationItemCopyWithImpl<$Res>
    implements $NavigationItemCopyWith<$Res> {
  _$NavigationItemCopyWithImpl(this._self, this._then);

  final NavigationItem _self;
  final $Res Function(NavigationItem) _then;

  /// Create a copy of NavigationItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? key = null,
    Object? isSelected = null,
  }) {
    return _then(_self.copyWith(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _self.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _NavigationItem implements NavigationItem {
  const _NavigationItem(
      {required this.title, required this.key, this.isSelected = false});

  @override
  final String title;
  @override
  final String key;
  @override
  @JsonKey()
  final bool isSelected;

  /// Create a copy of NavigationItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NavigationItemCopyWith<_NavigationItem> get copyWith =>
      __$NavigationItemCopyWithImpl<_NavigationItem>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NavigationItem &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected));
  }

  @override
  int get hashCode => Object.hash(runtimeType, title, key, isSelected);

  @override
  String toString() {
    return 'NavigationItem(title: $title, key: $key, isSelected: $isSelected)';
  }
}

/// @nodoc
abstract mixin class _$NavigationItemCopyWith<$Res>
    implements $NavigationItemCopyWith<$Res> {
  factory _$NavigationItemCopyWith(
          _NavigationItem value, $Res Function(_NavigationItem) _then) =
      __$NavigationItemCopyWithImpl;
  @override
  @useResult
  $Res call({String title, String key, bool isSelected});
}

/// @nodoc
class __$NavigationItemCopyWithImpl<$Res>
    implements _$NavigationItemCopyWith<$Res> {
  __$NavigationItemCopyWithImpl(this._self, this._then);

  final _NavigationItem _self;
  final $Res Function(_NavigationItem) _then;

  /// Create a copy of NavigationItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? key = null,
    Object? isSelected = null,
  }) {
    return _then(_NavigationItem(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _self.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
