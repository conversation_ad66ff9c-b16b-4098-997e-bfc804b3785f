import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import '../models/loan_detail_models.dart';
import '../provider/loan_detail_state.dart';

/// 贷款商品详情头部组件
class LoanDetailHeader extends StatelessWidget {
  final LoanDetailModel loanDetail;

  const LoanDetailHeader({
    super.key,
    required this.loanDetail,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBankInfo(),
          Divider(),
          _buildDetailItem(
            icon: Icons.percent,
            label: '年化利率',
            value:
                '${loanDetail.minAnnualRate}% - ${loanDetail.maxAnnualRate}%',
          ),
          FutureBuilder(
            builder: (context, snapshot) {
              return _buildDetailItem(
                icon: Icons.schedule,
                label: '周期',
                value: snapshot.data?.join('/') ?? "-",
              );
            },
            future: DictTypesHelper.batchFormat(
                loanDetail.loanTerm.split(','), DictTypeEnum.loanTermList),
          ),
          FutureBuilder(
            builder: (context, snapshot) {
              return _buildDetailItem(
                icon: Icons.payment,
                label: '还款方式',
                value: snapshot.data?.join('、') ?? "-",
              );
            },
            future: DictTypesHelper.batchFormat(
                loanDetail.repaymentType.split(','),
                DictTypeEnum.repaymentType),
          ),

        ],
      ),
    );
  }

  /// 构建银行信息
  Widget _buildBankInfo() {
    return Row(
      children: [
        // 银行Logo
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
              imageUrl: loanDetail.pic ?? '', width: 48, height: 48),
        ),
        const SizedBox(width: 12),
        // 银行名称和产品名称
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                loanDetail.name,
                style: const TextStyle(
                  color: AppColors.textColor1,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              _buildProductInfo()
            ],
          ),
        ),
      ],
    );
  }


  /// 构建产品信息
  Widget _buildProductInfo() {
    return Row(
      children: [
        Text(
          '最高额度',
          style: const TextStyle(
            color: AppColors.textColor1,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${loanDetail.maxQuota}万',
          style: const TextStyle(
            color: AppColors.orange,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }


  /// 构建详情项
  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    var textColor8 = AppColors.textColor8;
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          "$label:",
          style: TextStyle(
            color: textColor8,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: textColor8,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
