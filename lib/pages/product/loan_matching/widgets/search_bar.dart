import 'dart:math';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/home/<USER>/widgets/search_enterprise_viewmodel.dart';

class LoadMatchPageSearchBar extends StatefulWidget {
  LoadMatchPageSearchBar({super.key, required this.onSelected});

  ValueChanged<String> onSelected;

  @override
  State<LoadMatchPageSearchBar> createState() => _LoadMatchPageSearchBarState();
}

class _LoadMatchPageSearchBarState extends State<LoadMatchPageSearchBar> {
  // 当前页码
  int currentPage = 1;
  bool hasMorePage = false;

  // 刷新控制器
  EasyRefreshController easyRefreshController = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  @override
  void initState() {
    super.initState();
    // _debouncedSearch =
    //     _debounce<Iterable<SearchEnterprise>?, String>(_firstSearch);
  }

  TextEditingController? _textEditingController;

  FocusNode? _focusNode;

  List<SearchEnterprise> enterprises = [];

  _SearchBarViewModel viewModel = _SearchBarViewModel();

  @override
  Widget build(BuildContext context) {
    return _buildAutocomplete();
  }

  MyAutocomplete<SearchEnterprise> _buildAutocomplete() {
    return MyAutocomplete<SearchEnterprise>(
      fieldViewBuilder:
          (context, textEditingController, focusNode, onFieldSubmitted) {
        _textEditingController = textEditingController;
        _focusNode = focusNode;
        return _buildSearchField(
            context, textEditingController, focusNode, onFieldSubmitted);
      },
      optionsViewBuilder: (context, onSelected, options) {
        return _optionsList(onSelected);
      },
      optionsBuilder: (TextEditingValue textEditingValue) async {
        final text = textEditingValue.text;
        if (text == '') {
          await viewModel.searchKeywordRecommend();
          defaultLogger.info("optionsBuilder length:${viewModel.items.length}");
          return viewModel.items;
        } else {
          await viewModel.firstLoad(text);
          defaultLogger.info("optionsBuilder length:${viewModel.items.length}");
          return viewModel.items;
        }
      },
      onSelected: (option) async {
        _focusNode?.unfocus();

        /// 这里需要等待下 _focusNode.hasFocus 比为false。不然 _textEditingController?.text
        /// 又会触发搜索
        await Future.delayed(Duration(milliseconds: 400));
        widget.onSelected(option.companyName ?? '');
        _textEditingController?.text = option.companyName ?? "";
      },
    );
  }

  _optionsList(AutocompleteOnSelected<SearchEnterprise> onSelected) {
    defaultLogger.info("_optionsList 11111 length:${viewModel.items.length}");
    return StatefulBuilder(builder: (context, setListState) {
      defaultLogger.info("_optionsList length:${viewModel.items.length}");
      if (viewModel.items.isEmpty) {
        return SizedBox.shrink();
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(right: 60), // todo 查找为什么会有个偏移的原因
            decoration: BoxDecoration(
              color: Color(0xFFF7F7F7),
              borderRadius: BorderRadius.circular(10),
            ),
            height: min(140, max(viewModel.items.length * 40, 100)),
            child: EasyRefresh(
              controller: viewModel.easyRefreshController,
              onLoad: viewModel.hasMorePate
                  ? () async {
                      try {
                        await viewModel.loadMore();
                        setListState(() {});
                      } catch (error) {}
                    }
                  : null,
              child: ListView(
                key: ValueKey(viewModel.keyword),
                padding: EdgeInsets.only(top: 8, bottom: 5),
                children: [
                  ...viewModel.items.map((a) {
                    return GestureDetector(
                      onTap: () {
                        onSelected(a);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 20),
                        child: Text(
                          a.companyName ?? '',
                          style: TextStyle(
                              fontSize: 15, color: AppColors.textColor1),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
          Spacer(),
        ],
      );
    });
  }

  _buildSearchField(
      BuildContext context,
      TextEditingController textEditingController,
      FocusNode focusNode,
      VoidCallback onFieldSubmitted) {
    return StatefulBuilder(builder: (context, _setState) {
      return TextFormField(
        controller: textEditingController,
        focusNode: focusNode,
        onFieldSubmitted: (String value) {
          onFieldSubmitted();
        },
        onChanged: (value) {
          _setState(() {});
        },
        textInputAction: TextInputAction.search,
        style: TextStyle(color: Color(0xFF222222), fontSize: 14),
        decoration: InputDecoration(
          isDense: true,
          hintText: '请输入公司全称',
          hintStyle: TextStyle(
              fontSize: 14,
              color: Color(0xFFE6E6E6),
              fontWeight: FontWeight.w600,
              height: 20 / 14),
          hintMaxLines: 1,
          suffixIcon: (_textEditingController?.text ?? '').isNotEmpty
              ? GestureDetector(
                  onTap: () {
                    _textEditingController?.text = '';
                  },
                  child: Icon(
                    Icons.cancel,
                    size: 16,
                    color: AppColors.textColor8,
                  ),
                )
              : null,
          suffixIconConstraints: BoxConstraints.tightFor(width: 60, height: 40),
          contentPadding:
              EdgeInsetsDirectional.symmetric(horizontal: 0, vertical: 15),
          border: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.dividerColor),
          ),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.dividerColor),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.dividerColor),
          ),
        ),
      );
    });
  }
}

class _SearchBarViewModel extends SearchEnterpriseViewModel {
  Future<void> searchKeywordRecommend() async {
    try {
      final result = await QueryApi.searchKeyListAll();
      final list = result.data ?? [];
      final pagination = Pagination(
          pageNum: 1,
          pageSize: 1,
          totalPage: 1,
          total: list.length,
          list: list);
      items = list.map((item) {
        return SearchEnterprise(
          companyName: item.taxpayerName,
          creditNo: item.creditCode,
          companyCode: item.creditCode,
          legalPerson: "",
          companyStatus: "",
          establishDate: "",
        );
      }).toList();
      pageIndex = pagination.pageNum;
      hasMorePate = list.length < pagination.total;

      hasMorePate = false;
      if (hasMorePate) {
        easyRefreshController.finishLoad();
      } else {
        easyRefreshController.finishLoad(IndicatorResult.noMore);
      }
      defaultLogger.warning("搜索公司推荐成功, items:${list.length}");
    } catch (error) {
      defaultLogger.warning("搜索公司推荐失败，error:$error");
    }
  }
}
