import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';

/// 公司名称输入组件
class AgeInput extends StatelessWidget {
  final String value;
  final ValueChanged<String> onChanged;
  final String? errorText;

  const AgeInput({
    super.key,
    required this.value,
    required this.onChanged,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        RichText(
          text: const TextSpan(
            children: [
              TextSpan(
                text: '申请人年龄',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textColor1,
                ),
              ),
              TextSpan(
                text: ' *',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          onChanged: onChanged,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            hintText: '请输入申请人年龄',
            hintStyle: TextStyle(
              color: Colors.grey[400],
              fontSize: 16,
            ),
            filled: true,
            fillColor: Colors.white,
            border: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppColors.dividerColor),
            ),
            contentPadding: const EdgeInsets.symmetric(
              vertical: 12,
            ),
            errorText: errorText,
          ),
        ),
      ]),
    );
  }
}
