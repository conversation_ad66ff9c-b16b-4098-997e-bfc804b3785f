import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import '../provider/index.dart';
import '../models/index.dart';

/// 申请人身份选择组件
class ApplicantTypeSelector extends ConsumerWidget {
  final PersonStandItem? selectedType;
  final ValueChanged<PersonStandItem> onTypeChanged;

  const ApplicantTypeSelector({
    super.key,
    this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: const [
              Text(
                '申请人身份',
                style: TextStyle(
                  fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textColor1
                ),
              ),
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              _showApplicantTypeBottomSheet(context, ref);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Color(0xFFE0E0E0))),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    selectedType?.dictLabel ?? "选择申请人身份",
                    style: TextStyle(
                      fontSize: 16,
                      color: selectedType != null
                          ? AppColors.textColor1
                          : AppColors.textColor9,
                    ),
                  ),
                  const Icon(Icons.keyboard_arrow_right, color: Colors.black38),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示申请人身份选择底部弹窗
  void _showApplicantTypeBottomSheet(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Consumer(
          builder: (context, ref, child) {
            final personStandAsync = ref.watch(personStandNotifierProvider);

            return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 顶部拖拽指示器
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // 标题
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  '选择申请人身份',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),

              // 选项列表
                  personStandAsync.when(
                    data: (personStandList) {
                      return Column(
                        children: personStandList.map((type) {
                          return InkWell(
                            onTap: () {
                              Navigator.pop(context);
                              onTypeChanged(type);
                            },
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(
                                vertical: 16,
                                horizontal: 24,
                              ),
                              child: Text(
                                type.dictLabel,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  color:
                                      selectedType?.dictValue == type.dictValue
                                          ? AppColors.primary
                                          : Colors.black87,
                                  fontWeight:
                                      selectedType?.dictValue == type.dictValue
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    },
                    loading: () => const Padding(
                      padding: EdgeInsets.all(24.0),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                    error: (error, stack) => Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Center(
                        child: Column(
                          children: [
                            Text(
                              '加载失败: $error',
                              style: const TextStyle(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: () {
                                ref
                                    .read(personStandNotifierProvider.notifier)
                                    .refresh();
                              },
                              child: const Text('重试'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

              // 底部安全区域
              SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
            ],
          ),
        );
          },
        );
      },
    );
  }
}
