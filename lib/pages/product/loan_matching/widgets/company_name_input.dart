import 'package:flutter/material.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/home/<USER>/widgets/search_bar.dart';

import 'search_bar.dart';


/// 公司名称输入组件
class CompanyNameInput extends StatelessWidget {
  final String value;
  final ValueChanged<String> onChanged;
  final String? errorText;

  const CompanyNameInput({
    super.key,
    required this.value,
    required this.onChanged,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: const TextSpan(
              children: [
                TextSpan(
                  text: '公司名称',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textColor1,
                  ),
                ),
                TextSpan(
                  text: ' *',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.red,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          LoadMatchPageSearchBar(
            onSelected: (value) => onChanged(value),
          ),
         
        ],
      ),
    );
  }

}
