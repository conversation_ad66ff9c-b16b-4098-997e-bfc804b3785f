import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:zrreport/common/models/base_response.dart';

part 'person_stand_models.freezed.dart';
part 'person_stand_models.g.dart';

/// 申请人身份字典项
@freezed
abstract class PersonStandItem with _$PersonStandItem {
  const factory PersonStandItem({
    /// 字典编码
    required String dictCode,
    /// 排序
    @Default(0) int dictSort,
    /// 字典标签（显示名称）
    required String dictLabel,
    /// 字典值
    required String dictValue,
    /// 字典类型
    required String dictType,
    /// CSS类
    String? cssClass,
    /// 列表类
    String? listClass,
    /// 备注
    String? remark,
  }) = _PersonStandItem;

  factory PersonStandItem.fromJson(Map<String, dynamic> json) =>
      _$PersonStandItemFromJson(json);
}

/// 申请人身份字典数据
@freezed
abstract class PersonStandData with _$PersonStandData {
  const factory PersonStandData({
    /// 申请人身份列表
    @Default([]) @JsonKey(name: 'person_stand') List<PersonStandItem> personStand,
  }) = _PersonStandData;

  factory PersonStandData.fromJson(Map<String, dynamic> json) =>
      _$PersonStandDataFromJson(json);
}

/// 申请人身份响应
typedef PersonStandResponse = BaseResponse<PersonStandData>;
