// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'person_stand_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PersonStandItem {
  /// 字典编码
  String get dictCode;

  /// 排序
  int get dictSort;

  /// 字典标签（显示名称）
  String get dictLabel;

  /// 字典值
  String get dictValue;

  /// 字典类型
  String get dictType;

  /// CSS类
  String? get cssClass;

  /// 列表类
  String? get listClass;

  /// 备注
  String? get remark;

  /// Create a copy of PersonStandItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PersonStandItemCopyWith<PersonStandItem> get copyWith =>
      _$PersonStandItemCopyWithImpl<PersonStandItem>(
          this as PersonStandItem, _$identity);

  /// Serializes this PersonStandItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PersonStandItem &&
            (identical(other.dictCode, dictCode) ||
                other.dictCode == dictCode) &&
            (identical(other.dictSort, dictSort) ||
                other.dictSort == dictSort) &&
            (identical(other.dictLabel, dictLabel) ||
                other.dictLabel == dictLabel) &&
            (identical(other.dictValue, dictValue) ||
                other.dictValue == dictValue) &&
            (identical(other.dictType, dictType) ||
                other.dictType == dictType) &&
            (identical(other.cssClass, cssClass) ||
                other.cssClass == cssClass) &&
            (identical(other.listClass, listClass) ||
                other.listClass == listClass) &&
            (identical(other.remark, remark) || other.remark == remark));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, dictCode, dictSort, dictLabel,
      dictValue, dictType, cssClass, listClass, remark);

  @override
  String toString() {
    return 'PersonStandItem(dictCode: $dictCode, dictSort: $dictSort, dictLabel: $dictLabel, dictValue: $dictValue, dictType: $dictType, cssClass: $cssClass, listClass: $listClass, remark: $remark)';
  }
}

/// @nodoc
abstract mixin class $PersonStandItemCopyWith<$Res> {
  factory $PersonStandItemCopyWith(
          PersonStandItem value, $Res Function(PersonStandItem) _then) =
      _$PersonStandItemCopyWithImpl;
  @useResult
  $Res call(
      {String dictCode,
      int dictSort,
      String dictLabel,
      String dictValue,
      String dictType,
      String? cssClass,
      String? listClass,
      String? remark});
}

/// @nodoc
class _$PersonStandItemCopyWithImpl<$Res>
    implements $PersonStandItemCopyWith<$Res> {
  _$PersonStandItemCopyWithImpl(this._self, this._then);

  final PersonStandItem _self;
  final $Res Function(PersonStandItem) _then;

  /// Create a copy of PersonStandItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dictCode = null,
    Object? dictSort = null,
    Object? dictLabel = null,
    Object? dictValue = null,
    Object? dictType = null,
    Object? cssClass = freezed,
    Object? listClass = freezed,
    Object? remark = freezed,
  }) {
    return _then(_self.copyWith(
      dictCode: null == dictCode
          ? _self.dictCode
          : dictCode // ignore: cast_nullable_to_non_nullable
              as String,
      dictSort: null == dictSort
          ? _self.dictSort
          : dictSort // ignore: cast_nullable_to_non_nullable
              as int,
      dictLabel: null == dictLabel
          ? _self.dictLabel
          : dictLabel // ignore: cast_nullable_to_non_nullable
              as String,
      dictValue: null == dictValue
          ? _self.dictValue
          : dictValue // ignore: cast_nullable_to_non_nullable
              as String,
      dictType: null == dictType
          ? _self.dictType
          : dictType // ignore: cast_nullable_to_non_nullable
              as String,
      cssClass: freezed == cssClass
          ? _self.cssClass
          : cssClass // ignore: cast_nullable_to_non_nullable
              as String?,
      listClass: freezed == listClass
          ? _self.listClass
          : listClass // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _self.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PersonStandItem implements PersonStandItem {
  const _PersonStandItem(
      {required this.dictCode,
      this.dictSort = 0,
      required this.dictLabel,
      required this.dictValue,
      required this.dictType,
      this.cssClass,
      this.listClass,
      this.remark});
  factory _PersonStandItem.fromJson(Map<String, dynamic> json) =>
      _$PersonStandItemFromJson(json);

  /// 字典编码
  @override
  final String dictCode;

  /// 排序
  @override
  @JsonKey()
  final int dictSort;

  /// 字典标签（显示名称）
  @override
  final String dictLabel;

  /// 字典值
  @override
  final String dictValue;

  /// 字典类型
  @override
  final String dictType;

  /// CSS类
  @override
  final String? cssClass;

  /// 列表类
  @override
  final String? listClass;

  /// 备注
  @override
  final String? remark;

  /// Create a copy of PersonStandItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PersonStandItemCopyWith<_PersonStandItem> get copyWith =>
      __$PersonStandItemCopyWithImpl<_PersonStandItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PersonStandItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PersonStandItem &&
            (identical(other.dictCode, dictCode) ||
                other.dictCode == dictCode) &&
            (identical(other.dictSort, dictSort) ||
                other.dictSort == dictSort) &&
            (identical(other.dictLabel, dictLabel) ||
                other.dictLabel == dictLabel) &&
            (identical(other.dictValue, dictValue) ||
                other.dictValue == dictValue) &&
            (identical(other.dictType, dictType) ||
                other.dictType == dictType) &&
            (identical(other.cssClass, cssClass) ||
                other.cssClass == cssClass) &&
            (identical(other.listClass, listClass) ||
                other.listClass == listClass) &&
            (identical(other.remark, remark) || other.remark == remark));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, dictCode, dictSort, dictLabel,
      dictValue, dictType, cssClass, listClass, remark);

  @override
  String toString() {
    return 'PersonStandItem(dictCode: $dictCode, dictSort: $dictSort, dictLabel: $dictLabel, dictValue: $dictValue, dictType: $dictType, cssClass: $cssClass, listClass: $listClass, remark: $remark)';
  }
}

/// @nodoc
abstract mixin class _$PersonStandItemCopyWith<$Res>
    implements $PersonStandItemCopyWith<$Res> {
  factory _$PersonStandItemCopyWith(
          _PersonStandItem value, $Res Function(_PersonStandItem) _then) =
      __$PersonStandItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String dictCode,
      int dictSort,
      String dictLabel,
      String dictValue,
      String dictType,
      String? cssClass,
      String? listClass,
      String? remark});
}

/// @nodoc
class __$PersonStandItemCopyWithImpl<$Res>
    implements _$PersonStandItemCopyWith<$Res> {
  __$PersonStandItemCopyWithImpl(this._self, this._then);

  final _PersonStandItem _self;
  final $Res Function(_PersonStandItem) _then;

  /// Create a copy of PersonStandItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? dictCode = null,
    Object? dictSort = null,
    Object? dictLabel = null,
    Object? dictValue = null,
    Object? dictType = null,
    Object? cssClass = freezed,
    Object? listClass = freezed,
    Object? remark = freezed,
  }) {
    return _then(_PersonStandItem(
      dictCode: null == dictCode
          ? _self.dictCode
          : dictCode // ignore: cast_nullable_to_non_nullable
              as String,
      dictSort: null == dictSort
          ? _self.dictSort
          : dictSort // ignore: cast_nullable_to_non_nullable
              as int,
      dictLabel: null == dictLabel
          ? _self.dictLabel
          : dictLabel // ignore: cast_nullable_to_non_nullable
              as String,
      dictValue: null == dictValue
          ? _self.dictValue
          : dictValue // ignore: cast_nullable_to_non_nullable
              as String,
      dictType: null == dictType
          ? _self.dictType
          : dictType // ignore: cast_nullable_to_non_nullable
              as String,
      cssClass: freezed == cssClass
          ? _self.cssClass
          : cssClass // ignore: cast_nullable_to_non_nullable
              as String?,
      listClass: freezed == listClass
          ? _self.listClass
          : listClass // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _self.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$PersonStandData {
  /// 申请人身份列表
  @JsonKey(name: 'person_stand')
  List<PersonStandItem> get personStand;

  /// Create a copy of PersonStandData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PersonStandDataCopyWith<PersonStandData> get copyWith =>
      _$PersonStandDataCopyWithImpl<PersonStandData>(
          this as PersonStandData, _$identity);

  /// Serializes this PersonStandData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PersonStandData &&
            const DeepCollectionEquality()
                .equals(other.personStand, personStand));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(personStand));

  @override
  String toString() {
    return 'PersonStandData(personStand: $personStand)';
  }
}

/// @nodoc
abstract mixin class $PersonStandDataCopyWith<$Res> {
  factory $PersonStandDataCopyWith(
          PersonStandData value, $Res Function(PersonStandData) _then) =
      _$PersonStandDataCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'person_stand') List<PersonStandItem> personStand});
}

/// @nodoc
class _$PersonStandDataCopyWithImpl<$Res>
    implements $PersonStandDataCopyWith<$Res> {
  _$PersonStandDataCopyWithImpl(this._self, this._then);

  final PersonStandData _self;
  final $Res Function(PersonStandData) _then;

  /// Create a copy of PersonStandData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? personStand = null,
  }) {
    return _then(_self.copyWith(
      personStand: null == personStand
          ? _self.personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as List<PersonStandItem>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PersonStandData implements PersonStandData {
  const _PersonStandData(
      {@JsonKey(name: 'person_stand')
      final List<PersonStandItem> personStand = const []})
      : _personStand = personStand;
  factory _PersonStandData.fromJson(Map<String, dynamic> json) =>
      _$PersonStandDataFromJson(json);

  /// 申请人身份列表
  final List<PersonStandItem> _personStand;

  /// 申请人身份列表
  @override
  @JsonKey(name: 'person_stand')
  List<PersonStandItem> get personStand {
    if (_personStand is EqualUnmodifiableListView) return _personStand;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_personStand);
  }

  /// Create a copy of PersonStandData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PersonStandDataCopyWith<_PersonStandData> get copyWith =>
      __$PersonStandDataCopyWithImpl<_PersonStandData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PersonStandDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PersonStandData &&
            const DeepCollectionEquality()
                .equals(other._personStand, _personStand));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_personStand));

  @override
  String toString() {
    return 'PersonStandData(personStand: $personStand)';
  }
}

/// @nodoc
abstract mixin class _$PersonStandDataCopyWith<$Res>
    implements $PersonStandDataCopyWith<$Res> {
  factory _$PersonStandDataCopyWith(
          _PersonStandData value, $Res Function(_PersonStandData) _then) =
      __$PersonStandDataCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'person_stand') List<PersonStandItem> personStand});
}

/// @nodoc
class __$PersonStandDataCopyWithImpl<$Res>
    implements _$PersonStandDataCopyWith<$Res> {
  __$PersonStandDataCopyWithImpl(this._self, this._then);

  final _PersonStandData _self;
  final $Res Function(_PersonStandData) _then;

  /// Create a copy of PersonStandData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? personStand = null,
  }) {
    return _then(_PersonStandData(
      personStand: null == personStand
          ? _self._personStand
          : personStand // ignore: cast_nullable_to_non_nullable
              as List<PersonStandItem>,
    ));
  }
}

// dart format on
