import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/index.dart';
import 'package:zrreport/pages/product/quiz_preparation/index.dart';
import '../models/index.dart';

part 'loan_matching_provider.g.dart';

/// 匹配类型枚举
enum MatchingType {
  tax('税务匹配'),
  questionnaire('答题匹配');

  const MatchingType(this.displayName);
  final String displayName;
}



/// 贷款匹配页面状态
@riverpod
class LoanMatchingNotifier extends _$LoanMatchingNotifier {
  @override
  LoanMatchingState build() {
    return const LoanMatchingState();
  }

  /// 更新公司名称
  void updateCompanyName(String name) {
    state = state.copyWith(companyName: name);
  }

  /// 更新年龄
  void updateAge(int age) {
    state = state.copyWith(age: age);
  }

  /// 更新申请人身份
  void updateApplicantType(PersonStandItem type) {
    state = state.copyWith(applicantType: type);
  }

  /// 切换匹配类型
  void switchMatchingType(MatchingType type) {
    state = state.copyWith(matchingType: type);
  }

  /// 提交贷款匹配请求
  Future<void> submitLoanMatching(BuildContext context) async {
    if (!_validateForm()) return;

    state = state.copyWith(isSubmitting: true, submitError: null);

    Loading.show('正在查询...');

    CompanyInfoModel? companyInfoModel = null;

    try {
      final response = await QueryApi.queryCompanyInfo(state.companyName);
      defaultLogger.info('产品 - 税务匹配，公司信息查询成功, ${response.data}');

      companyInfoModel = response.data;
    } catch (e) {
      Loading.error("$e");
      defaultLogger.info('产品 - 税务匹配，公司信息查询失败, $e');
    } finally {
      Loading.dismiss();
    }

    state = state.copyWith(isSubmitting: false, submitError: null);

    if (companyInfoModel == null) return;

    // 检查context是否仍然有效
    if (!context.mounted) return;

    if (state.matchingType == MatchingType.tax) {
      // 税务匹配逻
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TaxSupplementPage(
            creditCode: companyInfoModel!.creditCodeOrTaxPayerId,
            enterpriseName: companyInfoModel.taxpayerName,
            provinceId: companyInfoModel.provinceId,
            personStand: state.applicantType!.dictValue,
          ),
        ),
      );

    } else {
      // 答题匹配逻辑

      QuizPreparationParams params = QuizPreparationParams(
          creditCode: companyInfoModel.creditCodeOrTaxPayerId,
          enterpriseName: companyInfoModel.taxpayerName,
          personStand: state.applicantType!.dictValue,
          age: state.age.toString(),
          areaId: companyInfoModel.provinceId,
          channelType: "routine");

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => QuizPreparationPage(
            params: params,
          ),
        ),
      );
    }
  }

  /// 验证表单
  bool _validateForm() {
    if (state.companyName.trim().isEmpty) {
      state = state.copyWith(submitError: '请输入公司名称');
      Loading.error("请输入公司名称");
      return false;
    }

    if (state.applicantType == null) {
      state = state.copyWith(submitError: '请选择申请人身份');
      Loading.error("请选择申请人身份");
      return false;
    }

    if (state.matchingType == MatchingType.questionnaire &&
        (state.age == null || state.age! < 18)) {
      state = state.copyWith(submitError: '请输入有效的年龄');
      Loading.error("请输入有效的年龄");
      return false;
    }

    return true;
  }

  /// 清除错误信息
  void clearErrors() {
    state = state.copyWith(
      submitError: null,
    );
  }
}

/// 贷款匹配状态模型
class LoanMatchingState {
  final String companyName;
  final int? age;
  final PersonStandItem? applicantType;
  final MatchingType matchingType;
  final bool isSubmitting;
  final String? submitError;

  const LoanMatchingState({
    this.companyName = '',
    this.age,
    this.applicantType,
    this.matchingType = MatchingType.tax,
    this.isSubmitting = false,
    this.submitError,
  });

  LoanMatchingState copyWith({
    String? companyName,
    int? age,
    PersonStandItem? applicantType,
    MatchingType? matchingType,
    bool? isSubmitting,
    String? submitError,
  }) {
    return LoanMatchingState(
      companyName: companyName ?? this.companyName,
      applicantType: applicantType ?? this.applicantType,
      age: age ?? this.age,
      matchingType: matchingType ?? this.matchingType,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      submitError: submitError,
    );
  }
}
