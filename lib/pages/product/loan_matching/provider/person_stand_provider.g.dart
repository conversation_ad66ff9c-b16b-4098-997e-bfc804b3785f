// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'person_stand_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$personStandNotifierHash() =>
    r'0466c188cc61ba12fc00eeb4a564f00ed8143dd3';

/// 申请人身份数据提供者
///
/// Copied from [PersonStandNotifier].
@ProviderFor(PersonStandNotifier)
final personStandNotifierProvider =
    AsyncNotifierProvider<PersonStandNotifier, List<PersonStandItem>>.internal(
  PersonStandNotifier.new,
  name: r'personStandNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$personStandNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PersonStandNotifier = AsyncNotifier<List<PersonStandItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
