import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/pages/product/loan_matching/provider/person_stand_provider.dart';
import 'package:zrreport/pages/product/loan_matching/models/person_stand_models.dart';

void main() {
  group('PersonStandProvider Tests', () {
    test('PersonStandData should parse real API response correctly', () {
      // 使用你提供的真实API响应数据
      final apiResponse = {
        "person_stand": [
          {
            "dictCode": "171",
            "dictSort": 0,
            "dictLabel": "法人",
            "dictValue": "1",
            "dictType": "person_stand",
            "cssClass": null,
            "listClass": "default",
            "remark": null
          },
          {
            "dictCode": "172",
            "dictSort": 0,
            "dictLabel": "最大股东",
            "dictValue": "2",
            "dictType": "person_stand",
            "cssClass": null,
            "listClass": "default",
            "remark": null
          },
          {
            "dictCode": "173",
            "dictSort": 0,
            "dictLabel": "股东",
            "dictValue": "3",
            "dictType": "person_stand",
            "cssClass": null,
            "listClass": "default",
            "remark": null
          },
          {
            "dictCode": "174",
            "dictSort": 0,
            "dictLabel": "其他",
            "dictValue": "0",
            "dictType": "person_stand",
            "cssClass": null,
            "listClass": "default",
            "remark": null
          }
        ]
      };

      final data = PersonStandData.fromJson(apiResponse);

      expect(data.personStand.length, 4);
      expect(data.personStand[0].dictLabel, '法人');
      expect(data.personStand[0].dictValue, '1');
      expect(data.personStand[1].dictLabel, '最大股东');
      expect(data.personStand[1].dictValue, '2');
      expect(data.personStand[2].dictLabel, '股东');
      expect(data.personStand[2].dictValue, '3');
      expect(data.personStand[3].dictLabel, '其他');
      expect(data.personStand[3].dictValue, '0');
    });
    test('PersonStandItem model should serialize correctly', () {
      final item = PersonStandItem(
        dictCode: '171',
        dictSort: 0,
        dictLabel: '法人',
        dictValue: '1',
        dictType: 'person_stand',
        cssClass: null,
        listClass: 'default',
        remark: null,
      );

      expect(item.dictCode, '171');
      expect(item.dictLabel, '法人');
      expect(item.dictValue, '1');
      expect(item.dictType, 'person_stand');
    });

    test('PersonStandItem should create from JSON correctly', () {
      final json = {
        'dictCode': '171',
        'dictSort': 0,
        'dictLabel': '法人',
        'dictValue': '1',
        'dictType': 'person_stand',
        'cssClass': null,
        'listClass': 'default',
        'remark': null,
      };

      final item = PersonStandItem.fromJson(json);
      expect(item.dictCode, '171');
      expect(item.dictLabel, '法人');
      expect(item.dictValue, '1');
      expect(item.dictType, 'person_stand');
    });

    test('PersonStandData should handle list correctly', () {
      final data = PersonStandData(
        personStand: [
          PersonStandItem(
            dictCode: '171',
            dictSort: 0,
            dictLabel: '法人',
            dictValue: '1',
            dictType: 'person_stand',
          ),
          PersonStandItem(
            dictCode: '172',
            dictSort: 0,
            dictLabel: '最大股东',
            dictValue: '2',
            dictType: 'person_stand',
          ),
        ],
      );

      expect(data.personStand.length, 2);
      expect(data.personStand[0].dictLabel, '法人');
      expect(data.personStand[1].dictLabel, '最大股东');
    });
  });
}
